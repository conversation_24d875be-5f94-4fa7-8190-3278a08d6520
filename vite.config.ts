import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'url';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig({
  base: '/vfyg',
  plugins: [
    vue(),
    AutoImport({
      imports: [
        'vue',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ]
        }
      ],
      dts: './auto-imports.d.ts',
    }),
    Components({
      resolvers: [NaiveUiResolver()],
      dts: './components.d.ts',
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 7002,
    strictPort: true,
    open: true,
    proxy: {
      '/aliapi/': {
        target: 'https://api.fyg.cn/',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aliapi/, ''),
      },
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/fyjs': {
        target: 'https://data.fyg.cn/fyjs/',
        // target: 'http://localhost:7001/fyjs/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fyjs/, ''),
      },
      '/fyschedule': {
        target: 'https://data.fyg.cn/fyschedule/',
        // target: 'https://localhost:7001/fyschedule2/',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fyschedule/, ''),
      },
    },
  },
  // build: {
  //   target: 'esnext',
  //   outDir: 'dist',
  //   assetsDir: 'assets',
  //   sourcemap: false,
  //   minify: 'terser',
  //   chunkSizeWarningLimit: 500,
  //   rollupOptions: {
  //     output: {
  //       manualChunks(id) {
  //         if (id.includes('node_modules')) {
  //           return id.toString().split('node_modules/')[1].split('/')[0].toString();
  //         }
  //       }
  //     }
  //   }
  // }
  // build打包构建配置
  build: {
    chunkSizeWarningLimit: 2000,
    // 打包输出的文件夹名称
    outDir: 'dist',
    // 静态资源文件保存的文件夹名称
    assetsDir: 'static',
    // 是否启用css代码拆分
    cssCodeSplit: true,
    // 打包构建后是否生成 source map 文件。
    sourcemap: true,
    // 打包构建时压缩混淆使用的混淆器
    minify: 'esbuild',
    // 自定义底层的 Rollup 打包配置（Rollup文档地址：https://cn.rollupjs.org/configuration-options/）
    rollupOptions: {
      // 输出配置
      output: {
        // 输出的文件自定义命名
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        // 分包
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return id
              .toString()
              .split('node_modules/')[1]
              .split('/')[0]
              .toString();
          }
        },
      },
    },
  },
});
