/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountEdit: typeof import('./src/components/fyg/accounting/counterparty/AccountEdit.vue')['default']
    BankDeposit: typeof import('./src/components/fyg/accounting/dailyReport/BankDeposit.vue')['default']
    CashFlowStatement: typeof import('./src/components/fyg/accounting/cashFlow/CashFlowStatement.vue')['default']
    DataSourceEdit: typeof import('./src/components/fyg/accounting/dataSource/DataSourceEdit.vue')['default']
    DraggableSetting: typeof import('./src/components/dragger/draggableSetting.vue')['default']
    EquityStructure: typeof import('./src/components/fyg/accounting/dailyReport/EquityStructure.vue')['default']
    Filter: typeof import('./src/components/Filter.vue')['default']
    IncomeAndExpenses: typeof import('./src/components/fyg/accounting/dailyReport/IncomeAndExpenses.vue')['default']
    MessageApi: typeof import('./src/components/MessageApi.vue')['default']
    MyDateFilterComponent: typeof import('./src/components/MyDateFilterComponent.vue')['default']
    MyFilterComponent: typeof import('./src/components/MyFilterComponent.vue')['default']
    MyNumberFilterComponent: typeof import('./src/components/MyNumberFilterComponent.vue')['default']
    MyTagComponent: typeof import('./src/components/MyTagComponent.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NH3: typeof import('naive-ui')['NH3']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NPopover: typeof import('naive-ui')['NPopover']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTable: typeof import('naive-ui')['NTable']
    NTableCashFlowStatement: typeof import('./src/components/fyg/accounting/cashFlow/NTableCashFlowStatement.vue')['default']
    NTableOperatingCashFlow: typeof import('./src/components/fyg/accounting/cashFlow/NTableOperatingCashFlow.vue')['default']
    NTag: typeof import('naive-ui')['NTag']
    NThing: typeof import('naive-ui')['NThing']
    ProjectEdit: typeof import('./src/components/fyg/accounting/projects/ProjectEdit.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
