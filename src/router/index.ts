import { createRouter, createWebHistory } from 'vue-router';
import VueCookies from 'vue-cookies';
import FyjsProjectManagementIndex from '../views/fyjs/project/index.vue'
import FygAccountingDailyReport from '../views/fyg/accounting/dailyReport.vue'
import FygAccountingCashFlow from '../views/fyg/accounting/cashFlow.vue';
import FygAccountingProjectEdit from '../views/fyg/accounting/projectEditor.vue';
import FygAccountingProjectList from '../views/fyg/accounting/projectList.vue';
import FygAccountingCounterpartyEdit from '../views/fyg/accounting/counterpartyAccountEditor.vue';
import FygAccountingCounterpartyList from '../views/fyg/accounting/counterpartyAccountListWithEdit.vue';
import FygAccountingCounterpartyTypeList from '../views/fyg/accounting/counterpartyAccountProjectTypeList.vue'
import FygAccountingEquityDistributionList from '../views/fyg/accounting/equityDistributionList.vue';
import FygAccountingBankAccountList from '../views/fyg/accounting/bankAccountList.vue';
import FygAccountingDataSourceEdit from '../views/fyg/accounting/dataSourceEditor.vue';
import FygAccountingDataSourceList from '../views/fyg/accounting/dataSourceListWithEdit.vue';
import FygAccountingDailyReportSummary from '../views/fyg/accounting/dailyReportSummary.vue';
import ProseMirrorDemo from '../views/proseMirror/index.vue'
const routes = [
    {
        path: '/prosemirror', name: 'ProseMirrorDemo', component: ProseMirrorDemo, meta: { title: 'ProseMirror编辑器'}
    },
    {
        path: '/fyjs', children: [{
            path: 'project', children: [{
                path: 'index', name: 'FyjsProjectManagementIndex', component: FyjsProjectManagementIndex, meta: { title: '建设工程项目'}
            }]
        }]
    },
    {
        path: '/fyg', children: [{
            path: 'accounting', children: [{
                path: 'summary', name: 'FygAccountingDailyReportSummary', component: FygAccountingDailyReportSummary, meta: { title: '报表汇总'}
            }, {
                path: 'daily/:id/:name', name: 'FygAccountingDailyReport', component: FygAccountingDailyReport, meta: { title: '会计日常报表'}
            }, {
                path: 'cashflow/:id/:name', name: 'FygAccountingCashFlow', component: FygAccountingCashFlow, meta: { title: '现金流量表'}
            }, {
                path: 'project', children: [{
                    path: 'list', name: 'FygAccountingProjectList', component: FygAccountingProjectList, meta: { title: '项目列表', keepAlive: true }
                },{
                    path: 'edit/:id', name: 'FygAccountingProjectEdit', component: FygAccountingProjectEdit, meta: { title: '项目编辑'}
                }]
            }, {
                path: 'counterparty', children: [{
                    path: 'equity/:id/:name', name: 'FygAccountingEquityDistributionList', component: FygAccountingEquityDistributionList, meta: { title: '股权分配列表'}
                },{
                    path: 'types/:id', name: 'FygAccountingCounterpartyTypeList', component: FygAccountingCounterpartyTypeList, meta: { title: '往来单位类型列表'}
                }, {
                    path: 'list', name: 'FygAccountingCounterpartyList', component: FygAccountingCounterpartyList, meta: { title: '往来单位列表'}
                }, {
                    path: 'edit/:id', name: 'FygAccountingCounterpartyEdit', component: FygAccountingCounterpartyEdit, meta: { title: '往来单位编辑'}
                }]
            }, {
                path: 'bank/:id/:name', name: 'FygAccountingBankAccountList', component: FygAccountingBankAccountList, meta: { title: '银行账户列表'}
            }, {
                path: 'datasource', children: [{
                    path: 'edit/:id', name: 'FygAccountingDataSourceEdit', component: FygAccountingDataSourceEdit, meta: { title: '数据源编辑'}
                }, {
                    path: 'list/:id/:name', name: 'FygAccountingDataSourceList', component: FygAccountingDataSourceList, meta: { title: '数据源列表'}
                }]
            }]
        }]
    },
    { path: '/', redirect: '/fyg/accounting/project/list' }
]

const router = createRouter({
    history: createWebHistory('/vfyg/'),
    routes,

});
const defaultTitle = '默认'
const vueCookies: any = VueCookies
router.beforeEach((to, _from, next) => {
    if (to.meta.keepHistory) {
        vueCookies.set('historyPath', to.fullPath)
    }
    document.title = (to.meta.title as string) || defaultTitle
    if (to.meta.requireAuth) {
        const token = vueCookies.get('x-token')
        const isLogin = !!token
        if (isLogin) {
            next()
        } else {
            next({ name: 'login' })
        }
    } else {
        next()
    }
})
export default router