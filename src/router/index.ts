import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';
import VueCookies from 'vue-cookies';

// 类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title?: string;
    requireAuth?: boolean;
    keepHistory?: boolean;
    keepAlive?: boolean;
  }
}

// 常量定义
const DEFAULT_TITLE = '默认';
const DEFAULT_META = {
  requireAuth: true,
  keepHistory: true,
} as const;

// 懒加载组件
const Login = () => import('../views/base/login.vue');
const ProseMirrorDemo = () => import('../views/proseMirror/index.vue');

// FYJS 模块
const FyjsProjectManagementIndex = () => import('../views/fyjs/project/index.vue');

// FYG Accounting 模块
const FygAccountingDailyReport = () => import('../views/fyg/accounting/dailyReport.vue');
const FygAccountingCashFlow = () => import('../views/fyg/accounting/cashFlow.vue');
const FygAccountingProjectEdit = () => import('../views/fyg/accounting/projectEditor.vue');
const FygAccountingProjectList = () => import('../views/fyg/accounting/projectList.vue');
const FygAccountingCounterpartyEdit = () => import('../views/fyg/accounting/counterpartyAccountEditor.vue');
const FygAccountingCounterpartyList = () => import('../views/fyg/accounting/counterpartyAccountListWithEdit.vue');
const FygAccountingCounterpartyTypeList = () => import('../views/fyg/accounting/counterpartyAccountProjectTypeList.vue');
const FygAccountingEquityDistributionList = () => import('../views/fyg/accounting/equityDistributionList.vue');
const FygAccountingBankAccountList = () => import('../views/fyg/accounting/bankAccountList.vue');
const FygAccountingDataSourceEdit = () => import('../views/fyg/accounting/dataSourceEditor.vue');
const FygAccountingDataSourceList = () => import('../views/fyg/accounting/dataSourceListWithEdit.vue');
const FygAccountingDailyReportSummary = () => import('../views/fyg/accounting/dailyReportSummary.vue');

// FYFC 模块
const FyfcReviewStaffEdit = () => import('../views/fyfc/review/staff/edit/index.vue');

// 路由配置
const routes: RouteRecordRaw[] = [
  // 登录页面 - 不需要认证
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { title: '登录', requireAuth: false }
  },

  // ProseMirror 编辑器
  {
    path: '/prosemirror',
    name: 'ProseMirrorDemo',
    component: ProseMirrorDemo,
    meta: { title: 'ProseMirror编辑器', ...DEFAULT_META }
  },

  // FYJS 模块
  {
    path: '/fyjs',
    children: [{
      path: 'project',
      children: [{
        path: 'index',
        name: 'FyjsProjectManagementIndex',
        component: FyjsProjectManagementIndex,
        meta: { title: '建设工程项目', ...DEFAULT_META }
      }]
    }]
  },

  // FYFC 模块
  {
    path: '/fyfc',
    children: [{
      path: 'review',
      children: [{
        path: 'staff/edit',
        name: 'FyfcReviewStaffEdit',
        component: FyfcReviewStaffEdit,
        meta: { title: '方远房地产集团员工月度绩效考核评分表编辑', ...DEFAULT_META }
      }]
    }]
  },

  // FYG 模块
  {
    path: '/fyg',
    children: [{
      path: 'accounting',
      children: [
        // 报表相关
        {
          path: 'summary',
          name: 'FygAccountingDailyReportSummary',
          component: FygAccountingDailyReportSummary,
          meta: { title: '报表汇总', ...DEFAULT_META }
        },
        {
          path: 'daily/:id/:name',
          name: 'FygAccountingDailyReport',
          component: FygAccountingDailyReport,
          meta: { title: '会计日常报表', ...DEFAULT_META }
        },
        {
          path: 'cashflow/:id/:name',
          name: 'FygAccountingCashFlow',
          component: FygAccountingCashFlow,
          meta: { title: '现金流量表', ...DEFAULT_META }
        },

        // 项目管理
        {
          path: 'project',
          children: [{
            path: 'list',
            name: 'FygAccountingProjectList',
            component: FygAccountingProjectList,
            meta: { title: '项目列表', keepAlive: true, ...DEFAULT_META }
          }, {
            path: 'edit/:id',
            name: 'FygAccountingProjectEdit',
            component: FygAccountingProjectEdit,
            meta: { title: '项目编辑', ...DEFAULT_META }
          }]
        },

        // 往来单位管理
        {
          path: 'counterparty',
          children: [{
            path: 'equity/:id/:name',
            name: 'FygAccountingEquityDistributionList',
            component: FygAccountingEquityDistributionList,
            meta: { title: '股权分配列表', ...DEFAULT_META }
          }, {
            path: 'types/:id',
            name: 'FygAccountingCounterpartyTypeList',
            component: FygAccountingCounterpartyTypeList,
            meta: { title: '往来单位类型列表', ...DEFAULT_META }
          }, {
            path: 'list',
            name: 'FygAccountingCounterpartyList',
            component: FygAccountingCounterpartyList,
            meta: { title: '往来单位列表', ...DEFAULT_META }
          }, {
            path: 'edit/:id',
            name: 'FygAccountingCounterpartyEdit',
            component: FygAccountingCounterpartyEdit,
            meta: { title: '往来单位编辑', ...DEFAULT_META }
          }]
        },

        // 银行账户
        {
          path: 'bank/:id/:name',
          name: 'FygAccountingBankAccountList',
          component: FygAccountingBankAccountList,
          meta: { title: '银行账户列表', ...DEFAULT_META }
        },

        // 数据源管理
        {
          path: 'datasource',
          children: [{
            path: 'edit/:id',
            name: 'FygAccountingDataSourceEdit',
            component: FygAccountingDataSourceEdit,
            meta: { title: '数据源编辑', ...DEFAULT_META }
          }, {
            path: 'list/:id/:name',
            name: 'FygAccountingDataSourceList',
            component: FygAccountingDataSourceList,
            meta: { title: '数据源列表', ...DEFAULT_META }
          }]
        }
      ]
    }]
  },

  // 默认重定向
  { path: '/', redirect: '/fyfc/review/staff/edit' }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory('/vfyg/'),
  routes,
});

// 类型安全的 cookies 实例
const vueCookies = VueCookies as {
  get: (key: string) => string | null;
  set: (key: string, value: string) => void;
};

// 认证检查函数
const checkAuth = (): boolean => {
  const token = vueCookies.get('x-token');
  return !!token;
};

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title || DEFAULT_TITLE;

  // 保存历史路径
  if (to.meta.keepHistory) {
    vueCookies.set('historyPath', to.fullPath);
  }

  // 认证检查
  if (to.meta.requireAuth) {
    if (checkAuth()) {
      next();
    } else {
      next({ name: 'login' });
    }
  } else {
    next();
  }
});

export default router;