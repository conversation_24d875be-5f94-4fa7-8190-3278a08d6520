<template>
    <n-tag :bordered="false" :color="colorObject">
        {{ internalValue }}
    </n-tag>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { ColorInfo } from '../utils/APIUtils'
interface Props {
    value?: string,
    colorObject: ColorInfo
}
const props = withDefaults(defineProps<Props>(), {});

const internalValue = ref(props.value);
const colorObject = ref<ColorInfo>(props.colorObject);

// Watch for external value changes
watch(() => props.value, (newValue, oldValue) => {
    internalValue.value = newValue;
});

watch(() => props.colorObject, (newValue, oldValue) => {
    colorObject.value = newValue
})
</script>
<style scoped></style>