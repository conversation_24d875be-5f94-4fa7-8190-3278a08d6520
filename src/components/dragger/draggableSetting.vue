<template>
    <n-list>
        <div ref="el" style="padding: 0px 10px 0px 10px;">
            <n-list-item class="x" v-for="(item, index) in list" :key="index">
                <n-thing>
                    <template #header>
                        {{ item.title }}
                    </template>
                    <template #header-extra>
                        <n-switch v-model:value="item.isActivated" />
                    </template>
                </n-thing>
            </n-list-item>
        </div>
    </n-list>
</template>
  
<script setup lang="ts">
import { reactive, ref, watch, onMounted } from 'vue'
import { type UseDraggableReturn, VueDraggable, useDraggable } from 'vue-draggable-plus'
import { doPostAsync } from '../../utils/AxiosUtil';
import { inject } from 'vue';
import { serializeColumns } from '../../utils/APIUtils'
// 从全局注入中获取 message 实例
const message: any = inject('message');
interface Props {
    userId?: string,
    appKey?: string,
    appRouter?: string,
    data?: any,
    id: number | undefined,
    appRemark?: string,
}

const props = withDefaults(defineProps<Props>(), {
    userId: () => '',
    appKey: () => '',
    appRouter: () => '',
    data: () => [],
    appRemark: () => '',
})
const list = reactive<any>(props.data)

const el = ref<any>()

const { start } = useDraggable(el, list, {
    animation: 150,
    ghostClass: 'ghost',
    onStart() {
        console.log('start')
    },
    onUpdate() {
        console.log('update')
    }
})
const saveSetting = async (data: any) => {
    const url = '/fyschedule/app/user/setting/save'
    const params = {
        userId: props.userId,
        appKey: props.appKey,
        appRouter: props.appRouter,
        id: props.id,
        appRemark: props.appRemark,
        appSetting: await getSerializedSetting(data)
    };
    try {
        const res: any = await doPostAsync(url, params);
        if (res.code === 0) {
            console.info(res.msg);
        } else {
            message.info(res.msg);
        }
    } catch (error) {
        console.error('Failed to save settings:', error);
    }
};
// 序列化
async function getSerializedSetting(value: any) {
    if (Array.isArray(value) && value.length > 0) {
        const serializedColumns = serializeColumns(value);
        return serializedColumns;
    }
    return undefined;
}

const emits = defineEmits(['update:column'])
watch(list, (newVal: any, oldVal: any) => {
    saveSetting(newVal)
    emits('update:column', newVal)
})
watch(() => props.data, (newVal: any, oldVal: any) => {
    console.log(`output->newVal`, newVal)
})
onMounted(() => {
    // console.log(`output->props.data`,props.data)
})
</script>
  
<style scoped>
.x :deep(.n-list-item__prefix) {
    margin-right: 0px;
}

.ghost {
    opacity: 0.5;
    background: #c8ebfb;
}
</style>
  