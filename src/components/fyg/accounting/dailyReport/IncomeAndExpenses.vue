<template>
    <n-space colspan="5" justify="end" style="background-color: #2d66a8; color: white;">
        <div style="margin-right: 10px;" v-if="isSelected" @click="handleSelect" :bordered="false">{{
            formatDate(dateRef,
                "yyyy年MM月DD日") }} 单位：万元</div>
        <n-date-picker v-else @update-value="handleConfirm" v-model:value="dateValue" type="date" />
    </n-space>
    <n-data-table :columns="columns" :data="data" :bordered="false" :row-class-name="rowClassName" />
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    import type { DataTableColumn, DataTableColumns } from 'naive-ui'
    import { NButton, NInput, NP, NPopconfirm, NSpace, useMessage } from 'naive-ui'
    const message = useMessage()
    import { formatDate } from '../../../../utils/xUtils';
    import { fetchDataAlt } from '../../../../utils/APIUtils';
import { doPutAsync } from '../../../../utils/AxiosUtil';
    const BASE_SERVER = '/fyschedule/fyg/accounting/report/'
    interface Props {
        projectId: number,
        editor: string,
        name: string,
    }
    const props = withDefaults(defineProps<Props>(), {
    })
    const projectId = ref<number>(props.projectId);
    const editor = ref<string>(props.editor);


    // 定义日期
    const isSelected = ref(true);
    function handleSelect() {
        isSelected.value = !isSelected.value;
    }

    function handleConfirm(val: number) {
        dateRef.value = val;
        getData();
        isSelected.value = !isSelected.value;
    }
    const dateRef = ref(Date.now());
    const dateValue = dateRef;
    // 定义表格每一行的数据类型
    type DataRow = {
        noteId?: number;
        index?: string;
        category?: string;
        dailyAmount?: string;
        totalAmount?: string;
        note?: string;
        fundDailyDimensionId?: number;
        editable?: boolean;
        [key: string]: any;
    };

    const noteColumn = reactive<DataTableColumn>({
        title: '备注', key: 'note', align: 'center', width: 200,
        render(row, index) {
            return h(NPopconfirm, {
                showIcon: false,
                onPositiveClick: () => handlePositiveClick(row, index),
                onNegativeClick: () => handleNegativeClick(row, index),
                onBeforeShow: () => {
                    // 在 Popconfirm 打开前保存原始值
                    if (!('originalNote' in row)) {
                        row.originalNote = row.note;
                    }
                }
            }, {
                trigger: () => h(NButton, {
                    quaternary: true,
                    size: 'small'
                }, {
                    default: () => row.note || ''
                }),
                default: () => h(NInput, {
                    value: row.note as string, // 确保输入框绑定了当前值
                    onFocus: () => {
                        // 保存原始值
                        if (!row.originalNote) {
                            row.originalNote = row.note;
                        }
                    },
                    onUpdateValue: (val) => {
                        row.note = val; // 双向绑定值
                    }
                })
            },)
        }
    })
    // 列的数据
    const columns = reactive<DataTableColumns>([
        { title: '序号', key: 'index', align: 'center', width: 100 },
        { title: '现金流科目', key: 'category', align: 'center', width: 300 },
        { title: '当日发生金额', key: 'dailyAmount', align: 'center', width: 200 },
        { title: '累计金额', key: 'totalAmount', align: 'center', width: 200 },
        noteColumn,
    ]);



    async function handlePositiveClick(row: DataRow, index: number) {
        // 清除原始值
        delete row.originalNote;
        await handleSave(row);
        
    }

    async function handleSave(row: DataRow) {
        row.dataStatus = 1;
        row.notes = row.note;
        row.projectId = projectId.value;
        row.updatedBy = editor.value;
        row.createdBy = editor.value;
        row.id = row.noteId;
        const url = BASE_SERVER + 'save';
        const result:any = await doPutAsync(url, row);
        if (result.code === 200) {
            row.noteId = result.data.id;
            message.success(`备注已保存: ${row.note}`);
        }
    }
    function handleNegativeClick(row: DataRow, index: number) {
        // 恢复原始值
        row.note = row.originalNote || '';
        message.info('取消编辑，已恢复原值');
    }
    // 表格的数据
    const data = ref<DataRow[]>([
        // { index: '一', category: '收入合计', dailyAmount: '0.00', totalAmount: '427,352.13', },
        // { index: '1', category: '销售收入', dailyAmount: '-', totalAmount: '65,076.95', },
        // { index: '二', category: '支出合计', dailyAmount: '0.00', totalAmount: '384,841.39', },
        // { index: '1', category: '土地支出', dailyAmount: '-', totalAmount: '155,739.16', },
        // { index: '三', category: '资金余额', dailyAmount: '0.00', totalAmount: '42,510.74', },
        // { index: '1', category: '1.中行3934', dailyAmount: '-', totalAmount: '66.27', note: '基本户1.55%'},
    ]);

    async function getData() {
        const url = BASE_SERVER + 'daily/show';
        const params = {
            projectId: projectId.value,
            transactionDate: dateValue.value,
        };
        const results = await fetchDataAlt(url, params);
        if (results.length > 0) {
            data.value = results
        }
    }

    onMounted(() => {
        getData();
    });

    // 行样式
    const rowClassName = (row: DataRow) => {
        if (row.index === '一' || row.index === '二' || row.index === '三') {
            return 'separate-row';
        }
        return '';
    }
</script>
<style scoped>
    :deep(.separate-row td) {
        background-color: #2d66a8;
        color: white;
        text-align: center;
        padding: 10px;
    }


    td {
        text-align: center;
        padding: 8px;
    }

    .n-data-table {
        width: 100%;
    }
</style>