<template>
    <n-table :bordered="true" class="custom-table">
        <colgroup>
            <col style="width: 100px;">
            <col style="width: 300px;">
            <col style="width: 200px;">
            <col style="width: 200px;">
            <col style="width: 200px;">
        </colgroup>
        <tbody>
            <tr v-for="(row, index) in data" :key="index" :class="getRowClass(row)">
                <template v-if="row.type === 'section'">
                    <td :colspan="3">
                        {{ row.title }}
                    </td>
                    <td class="center">{{ row.amount2 }}</td>
                    <td class="center"> </td>
                </template>

                <template v-else>
                    <td class="center">{{ row.index }}</td>
                    <td class="center">{{ row.name }}</td>
                    <td class="center">{{ row.percentage }}</td>
                    <!-- <td class="center">{{ row.amount1 }}</td> -->
                    <td class="center">{{ row.amount2 }}</td>
                    <td class="center">{{ row.note }}</td>
                </template>
            </tr>
        </tbody>
    </n-table>
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { fetchDataAlt } from '../../../../utils/APIUtils';
    const BASE_SERVER = '/fyschedule/fyg/accounting/report/'
    interface Props {
        projectId: number,
    }
    const props = withDefaults(defineProps<Props>(), {
    })
    const projectId = ref<number>(props.projectId);

    interface DataItem {
        type?: string
        title?: string
        index?: string
        name?: string
        percentage?: string
        amount1?: string
        amount2?: string,
        note?: string
    }

    const data = ref<DataItem[]>([
        // { type: 'section', title: '占用工商股东资金净额', amount1: '66,550.00', },
        // { index: '1', name: '方远房产', percentage: '48.0%', amount1: '', amount2: '31,944.00', note: '' },
        // { type: 'section', title: '占用实际股东资金净额', amount1: '66,550.00', },
        // { index: '1', name: '方远房产', percentage: '28.0%', amount1: '', amount2: '18,634.00' },
        // { type: 'row', name: '金融机构融资净额', amount1: '99,800.00', amount2: '2.13%' },
        // { type: 'row', name: '项目公司借款净额', amount1: '0.00', amount2: '' },
    ])

    async function getData() {
        const url = BASE_SERVER + 'daily/show-equity';
        const params = {
            projectId: projectId.value,
        };
        const results = await fetchDataAlt(url, params);
        if (results.length > 0) {
            data.value = results
        }
    }

    onMounted(() => {
        getData();
    });

    const getRowClass = (row: DataItem) => {
        if (row.type === 'section') {
            return 'section-row'
        } else if (row.type === 'row') {
            return 'summary-row'
        } else {
            return ''
        }
    }
</script>

<style scoped>
    .section-row td {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .summary-row td {
        font-weight: bold;
    }

    .custom-table .center {
        text-align: center;
        vertical-align: middle;
        /* 如果需要垂直居中 */
    }
</style>