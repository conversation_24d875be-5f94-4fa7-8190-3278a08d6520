<template>
    <n-space colspan="5" justify="start">
        <div>银行存款</div>
    </n-space>
</template>
<script setup lang="ts">
    import { ref } from 'vue';
    import type { DataTableColumns } from 'naive-ui'
     // 定义表格每一行的数据类型
     type DataRow = {
        index?: string;
        category?: string;
        dailyAmount?: string;
        totalAmount?: string;
        note?: string;
    };
</script>