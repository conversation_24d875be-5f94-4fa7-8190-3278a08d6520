import { SETTING } from "../../../../utils/TableUtils";

export type TableData = {
    content?: Counterparty[],
    totalElements?: number,
    totalPages?: number
}

export type Counterparty = {
    id?: number,
    accountName?: string,
    dataStatus?: number; // 数据状态
    createdAt?: number; // 创建时间
    updatedAt?: number; // 更新时间
    createdBy?: string; // 创建者
    updatedBy?: string; // 最后编辑者
    [key: string]: any;
}

// 类型定义 & 封装方法

export type CounterpartyAccountProjectType = {
    id?: number; // 主键
    projectId?: number; // 项目ID，关联projects表
    counterpartyAccountId?: number; // 对方户名ID，关联counterparty_accounts表
    type?: string; // 对方户名类型
    dataStatus?: number; // 数据状态，1表示正常，0表示删除
    createdAt?: number; // 创建时间
    updatedAt?: number; // 更新时间
    createdBy?: string; // 创建者
    updatedBy?: string; // 最后编辑者
    [key: string]: any;
};

export const counterpartyAccountTypeOptions = [
    {
        label: '股东方',
        value: '股东方',
    },
    {
        label: '内部单位',
        value: '内部单位',
    },
    {
        label: '外部单位',
        value: '外部单位',
    },
    {
        label: '贷款银行',
        value: '贷款银行',
    },
];

export type QueryParam = {
    dataStatus?: number,
    accountName?: string,
    currentPage?: number,
    pageSize?: number,
    order?: string,
    columnKey?: string,
    [key: string]: any
}

export const DEFAULT_SETTING: SETTING[] = [
    { key: "accountName", title: "对方户名", isActivated: true },
];

export const fetchTestData = async () => {
    return {
        content: [{
            id: 1,
            accountName: "test",
            dataStatus: 1,
            createdAt: new Date().getTime(),
        }],
        totalElements: 1,
        totalPages: 1
    }
}