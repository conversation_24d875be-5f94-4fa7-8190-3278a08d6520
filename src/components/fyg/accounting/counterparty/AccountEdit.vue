<template>
    <n-form ref="formRef" :model="model" :rules="rules" :style="formStyle">
        <n-form-item path="accountName" label="对方户名">
            <n-input v-model:value="model.accountName" @keydown.enter.prevent />
        </n-form-item>
    </n-form>
</template>
<script setup lang="ts">
    import { ref, reactive } from "vue";
    import type { Counterparty } from ".";
    import type { FormInst, FormItemInst, FormItemRule, FormRules } from 'naive-ui'
    import { useMessage } from 'naive-ui'

    interface Props {
        width: number
    }
    const props = withDefaults(defineProps<Props>(), {
        width: 300,
    })

    interface FormStyle {
        width: string
    }

    const formStyle: FormStyle = {
        width: props.width + 'px'
    }

    const formRef = ref<FormInst | null>(null)
    const message = useMessage()
    const model = reactive<Counterparty>({})

    const rules: FormRules = {
        accountName: [
            { required: true, message: '请输入对方户名', trigger: 'blur' },

        ]
    }

    async function handleValidateButtonClick() {

        // 使用 Promise 封装 validate 的回调
        return new Promise((resolve, reject) => {
            formRef.value?.validate((errors) => {
                if (!errors) {
                    message.success('验证成功');
                    resolve(1); // 验证成功，解决 Promise
                } else {
                    console.log(errors);
                    message.error('验证失败');
                    reject(errors); // 验证失败，拒绝 Promise
                }
            });
        });
    }
    const isSubmitting = ref(false)
    async function doSave(e: MouseEvent) {
        e.preventDefault(); // 防止默认行为，如提交表单等
        isSubmitting.value = true

        try {
            // 先进行表单验证
            await handleValidateButtonClick(); // 等待验证结果

            // 如果验证成功，则继续执行保存操作
            await new Promise((resolve) => setTimeout(resolve, 1000))
            message.success('保存成功')
        } catch (errors) {
            // 如果验证失败，则在此处处理错误，例如显示错误信息或进行其他操作
            console.error('保存失败，因为验证未通过：', errors);
        } finally {
            isSubmitting.value = false; // 无论成功还是失败，都重置提交状态
        }
    }

    defineExpose({ doSave, })

    watch(() => props.width, (newWidth) => {
        formStyle.width = newWidth - 30 + 'px'
    })
</script>