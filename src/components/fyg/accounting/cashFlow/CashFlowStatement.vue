<template>
    <!-- 动态表格 -->
    <n-data-table :columns="columns" :data="data" />
    <!-- 添加月份按钮 -->
    <n-button @click="addNewMonth">添加新月份</n-button>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  import { DataTableColumn } from 'naive-ui';
  
  // 表格行的数据结构
  type TableRow = {
    incomeTotal: number;
    salesIncome: number;
    otherIncome: number;
    expenseTotal: number;
    landExpense: number;
    projectExpense: number;
    taxExpense: number;
    salesExpense: number;
    adminExpense: number;
    financialExpense: number;
    otherExpense: number;
    netOperatingCashflow: number;
    cumulativeOperatingCashflow: number;
    financingInflow: number;
    financingOutflow: number;
    shareholderCashflowInflow: number;
    shareholderCashflowOutflow: number;
    [key: string]: string | number; // 动态月份数据
  };
  
  // 定义固定列
  const fixedColumns = ref<DataTableColumn[]>([
    { title: '收入合计', key: 'incomeTotal' },
    { title: '销售收入', key: 'salesIncome' },
    { title: '其他收入', key: 'otherIncome' },
    { title: '支出合计', key: 'expenseTotal' },
    { title: '土地支出', key: 'landExpense' },
    { title: '工程支出', key: 'projectExpense' },
    { title: '税金支出', key: 'taxExpense' },
    { title: '销售费用', key: 'salesExpense' },
    { title: '管理费用', key: 'adminExpense' },
    { title: '财务费用', key: 'financialExpense' },
    { title: '其他支出', key: 'otherExpense' },
    { title: '当期经营性净现金流量', key: 'netOperatingCashflow' },
    { title: '累计经营性净现金流量', key: 'cumulativeOperatingCashflow' },
    { title: '融资流入', key: 'financingInflow' },
    { title: '融资还款支出', key: 'financingOutflow' },
    { title: '当期股东净现金流量', key: 'shareholderCashflowInflow' },
    { title: '累计股东净现金流量', key: 'shareholderCashflowOutflow' }
  ]);
  
  // 动态月份列
  const dynamicColumns = ref<DataTableColumn[]>([
    { title: '2023年3月', key: 'march2023' },
    { title: '2023年4月', key: 'april2023' },
    { title: '2023年5月', key: 'may2023' },
    { title: '2023年6月', key: 'june2023' }
  ]);
  
  // 合并固定列和动态列
  const columns = ref([...fixedColumns.value, ...dynamicColumns.value]);
  
  // 表格数据
  const data = ref<TableRow[]>([
    {
      incomeTotal: 65494.29,
      salesIncome: 65076.96,
      otherIncome: 417.33,
      expenseTotal: 189333.53,
      landExpense: 155739.16,
      projectExpense: 26395.24,
      taxExpense: 3327.97,
      salesExpense: 2043.79,
      adminExpense: 1234.59,
      financialExpense: -1824.91,
      otherExpense: 52.25,
      netOperatingCashflow: -30020.00,
      cumulativeOperatingCashflow: -123839.24,
      financingInflow: 100.00,
      financingOutflow: 200.00,
      shareholderCashflowInflow: 99000.00,
      shareholderCashflowOutflow: 158550.00,
      march2023: '-',
      april2023: 2320.00,
      may2023: -1697.00,
      june2023: 36.00
    },
    // 添加更多行数据...
  ]);
  
  // 当前月份索引，用于生成新列
  let currentMonthIndex = 6;
  
  // 添加新月份列的函数
  const addNewMonth = () => {
    currentMonthIndex++;
    const newMonth = `2023年${currentMonthIndex}月`;
    const newKey = `month${currentMonthIndex}`;
  
    // 添加新的动态列
    dynamicColumns.value.push({
      title: newMonth,
      key: newKey
    });
  
    // 更新合并后的所有列
    columns.value = [...fixedColumns.value, ...dynamicColumns.value];
  
    // 为表格数据中的每一行添加新列数据
    data.value.forEach((row) => {
      row[newKey] = '-'; // 为新月份列设置默认值
    });
  };
  </script>