<template>
    <n-table :bordered="true" class="fixed-table">
        <!-- 表头 -->
        <thead>
            <tr>
                <!-- 第一列：现金流量表 -->
                <th class="first-column">现金流量表</th>
                <!-- 动态生成月份列 -->
                <th v-for="month in months" :key="month">{{ month }}</th>
            </tr>
        </thead>

        <!-- 表格内容 -->
        <tbody>
            <tr v-for="(row, rowIndex) in tableData" :key="rowIndex" :class="rowClassName(row)">
                <!-- 第一列：固定的项目名称 -->
                <td :class="tdClassName(row)">{{ row.name }}</td>
                <!-- 动态生成的月份数据 -->
                <td v-for="month in months" :key="month">{{ row.data[month] }}</td>
            </tr>
        </tbody>
    </n-table>
</template>

<script setup lang="ts">
    import { ref } from 'vue'

    const rowClassName = (row: any) => {
        if (row.type === 'main') {
            return 'separate-row'
        }
        return ''
    }

    const tdClassName = (row: any) => {
        if (row.type === 'main') {
            return 'main-title'
        } else if (row.type === 'sub') {
            return 'sub-title'
        }
        return ''
    }

    // 定义月份（可以根据需求动态添加更多月份）
    const months = ref(['合计', '2023年3月', '2023年4月', '2023年5月', '2023年6月', '2023年7月', '2023年8月', '2023年9月', '2023年10月', '2023年11月', '2023年12月',
        '2024年1月', '2024年2月', '2024年3月'])

    // 定于表格数据结构
    type TableData = {
        type?: 'main' | 'sub';
        name?: string;
        data: {
            [key: string]: number | string | undefined;
        };
    };

    // 定义表格数据
    const tableData = ref<TableData[]>([
        {
            type: 'main',
            name: '收入合计',
            data: {
                '合计': '65,494.29',
                '2023年3月': '-',
                '2023年4月': '2,320.00',
                '2023年5月': '-1,697.00'
            }
        },
        {
            type: 'sub',
            name: '销售收入',
            data: {
                '合计': '65,076.96',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '其他收入',
            data: {
                '合计': '417.33',
                '2023年3月': '-',
                '2023年4月': '2,320.00',
                '2023年5月': '-1,697.00'
            }
        },
        {
            type: 'main',
            name: '支出合计',
            data: {
                '合计': '189,333.53',
                '2023年3月': '30,020.00',
                '2023年4月': '-',
                '2023年5月': '46,167.51'
            }
        },
        {
            type: 'sub',
            name: '土地支出',
            data: {
                '合计': '155,739.16',
                '2023年3月': '30,020.00',
                '2023年4月': '-',
                '2023年5月': '46,166.16'
            }
        },
        {
            type: 'sub',
            name: '工程支出',
            data: {
                '合计': '26,395.34',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '0.83'
            }
        },
        {
            type: 'sub',
            name: '税金支出',
            data: {
                '合计': '3,327.97',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '销售费用',
            data: {
                '合计': '2,004.73',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '管理费用',
            data: {
                '合计': '1,234.59',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '0.46'
            }
        },
        {
            type: 'sub',
            name: '财务费用',
            data: {
                '合计': '-182.91',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '0.06'
            }
        },
        {
            type: 'sub',
            name: '融资费用',
            data: {
                '合计': '762.41',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '其他支出',
            data: {
                '合计': '52.25',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '当期经营性净现金流量',
            data: {
                '合计': '-123,839.24',
                '2023年3月': '-30,020.00',
                '2023年4月': '2,320.00',
                '2023年5月': '-47,864.51'
            }
        },
        {
            type: 'main',
            name: '累计经营性净现金流量',
            data: {
                '合计': '-123,839.24',
                '2023年3月': '-30,020.00',
                '2023年4月': '-27,700.00',
                '2023年5月': '-75,564.51'
            }
        },
        {
            type: 'sub',
            name: '融资流入',
            data: {
                '合计': '100,000.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '融资还贷支出',
            data: {
                '合计': '200.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '当期融资净现金流量',
            data: {
                '合计': '99,800.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '累计融资净现金流量',
            data: {
                '合计': '99,800.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '资金流入-股东方',
            data: {
                '合计': '158,550.00',
                '2023年3月': '30,020.00',
                '2023年4月': '-',
                '2023年5月': '47,530.00'
            }
        },
        {
            type: 'sub',
            name: '资金流出-股东方',
            data: {
                '合计': '92,000.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '当期股东往来净额',
            data: {
                '合计': '66,550.00',
                '2023年3月': '30,020.00',
                '2023年4月': '-',
                '2023年5月': '47,530.00'
            }
        },
        {
            type: 'main',
            name: '累计股东往来净额',
            data: {
                '合计': '66,550.00',
                '2023年3月': '30,020.00',
                '2023年4月': '30,020.00',
                '2023年5月': '77,550.00'
            }
        },
        {
            type: 'sub',
            name: '资金流入-内部单位',
            data: {
                '合计': '-',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '资金流入-外部单位',
            data: {
                '合计': '103,307.85',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '资金流出-内部单位',
            data: {
                '合计': '-',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '资金流出-外部单位',
            data: {
                '合计': '103,307.85',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '当期单位往来净额',
            data: {
                '合计': '-0.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '累计单位往来净额',
            data: {
                '合计': '-0.00',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '当期现金净流量',
            data: {
                '合计': '42,510.76',
                '2023年3月': '-',
                '2023年4月': '2,320.00',
                '2023年5月': '-334.51'
            }
        },
        {
            type: 'main',
            name: '期末货币资金余额',
            data: {
                '合计': '42,510.76',
                '2023年3月': '-',
                '2023年4月': '2,320.00',
                '2023年5月': '1,985.49'
            }
        },
        {
            type: 'main',
            name: '不可动资金',
            data: {
                '合计': '-',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'main',
            name: '可动用资金余额',
            data: {
                '合计': '-',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
    ])
</script>

<style scoped>

    /* 强制表格使用固定布局 */
    .fixed-table {
        table-layout: fixed;
        width: 100%;
    }

    .first-column {
        width: 140px;
        /* 设置列宽 */
    }

    :deep(.separate-row td) {
        background-color: #2d66a8;
        color: white;
    }

    :deep(.main-title) {
        text-align: left;
    }

    :deep(.sub-title) {
        text-align: center;
    }

    td {
        text-align: right;
    }
</style>