<template>
    <n-table :bordered="true" class="fixed-table">
        <!-- 表头 -->
        <thead>
            <tr>
                <th class="first-column"></th>
                <!-- 动态生成月份列 -->
                <th v-for="month in months" :key="month">{{ month }}</th>
            </tr>
        </thead>

        <!-- 表格内容 -->
        <tbody>
            <tr v-for="(row, rowIndex) in tableData" :key="rowIndex" :class="rowClassName(row)">
                <!-- 第一列：固定的项目名称 -->
                <td :class="tdClassName(row)">{{ row.name }}</td>
                <!-- 动态生成的月份数据 -->
                <td v-for="month in months" :key="month">{{ row.data[month] }}</td>
            </tr>
        </tbody>
    </n-table>
</template>

<script setup lang="ts">
    import { ref } from 'vue'

    const rowClassName = (row: any) => {
        if (row.type === 'main') {
            return 'separate-row'
        }
        return ''
    }

    const tdClassName = (row: any) => {
        if (row.type === 'main') {
            return 'main-title'
        } else if (row.type === 'sub') {
            return 'sub-title'
        }
        return ''
    }

    // 定义月份（可以根据需求动态添加更多月份）
    const months = ref(['经营现金流', '2023年3月', '2023年4月', '2023年5月', '2023年6月', '2023年7月', '2023年8月', '2023年9月', '2023年10月', '2023年11月', '2023年12月',
        '2024年1月', '2024年2月', '2024年3月'])

    // 定于表格数据结构
    type TableData = {
        type?: 'main' | 'sub';
        name?: string;
        data: {
            [key: string]: number | string | undefined;
        };
    };

    // 定义表格数据
    const tableData = ref<TableData[]>([
        {
            type: 'sub',
            name: '',
            data: {
                '经营现金流': '房款收入',
                '2023年3月': '-',
                '2023年4月': '-',
                '2023年5月': '-'
            }
        },
        {
            type: 'sub',
            name: '',
            data: {
                '经营现金流': '经营支出',
                '2023年3月': '-',
                '2023年4月': '2,320.00',
                '2023年5月': '-1,697.00'
            }
        },
    ])
</script>

<style scoped>

    /* 强制表格使用固定布局 */
    .fixed-table {
        table-layout: fixed;
        width: 100%;
    }

    .first-column {
        width: 140px;
        /* 设置列宽 */
    }

    :deep(.separate-row td) {
        background-color: #2d66a8;
        color: white;
    }

    :deep(.main-title) {
        text-align: left;
    }

    :deep(.sub-title) {
        text-align: center;
    }

    td {
        text-align: right;
    }
</style>