<template>
    <n-form ref="formRef" :model="model" :rules="rules" :style="formStyle">
        <n-form-item path="bankAccountId" label="银行账户">
            <n-select v-model:value="model.bankAccountId" placeholder="请选择银行账户" :options="bankOptions" />
        </n-form-item>
        <n-form-item path="transactionDate" label="交易日期">
            <n-date-picker v-model:value="model.transactionDate" placeholder="请选择交易日期" type="date" />
        </n-form-item>
        <n-form-item path="incomeAmount" label="收入金额">
            <n-input-number v-model:value="model.incomeAmount" placeholder="请输入收入金额" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item path="expenseAmount" label="支出金额">
            <n-input-number v-model:value="model.expenseAmount" placeholder="请输入支出金额" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item path="fundDailyDimensionId" label="资金日报维度">
            <n-select v-model:value="model.fundDailyDimensionId" placeholder="请选择资金日报维度"
                :options="fundDailyDimensionOptions" />
        </n-form-item>
        <n-form-item v-if="!isRelevantCounterparty" path="counterpartyName" label="对方名称">
            <n-input v-model:value="model.counterpartyName" placeholder="请输入对方名称" />
        </n-form-item>
        <n-form-item v-if="isRelevantCounterparty" path="counterpartyAccountId" label="对方户名">
            <n-select v-model:value="model.counterpartyAccountId" placeholder="请选择对方户名"
                :options="counterpartyOptions" />
        </n-form-item>
        <n-form-item v-if="isTaxDetailRequired" path="taxDetail" label="税金明细">
            <n-select v-model:value="model.taxDetail" placeholder="请选择税金明细" :options="taxDetailOptions" />
        </n-form-item>
        <n-form-item v-if="isDevelopmentCostDetailRequired" path="developmentCostDetail" label="开发成本明细">
            <n-select v-model:value="model.developmentCostDetail" placeholder="请选择开发成本明细"
                :options="developmentCostDetailOptions" />
        </n-form-item>
        <n-form-item v-if="model.fundDailyDimensionId === 6" path="annualInterestRate" label="年利率">
            <n-input-number v-model:value="model.annualInterestRate" placeholder="请输入年利率" :show-button="false">
                <template #suffix>
                    %
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item path="projectId" label="项目">
            <n-select v-model:value:value="model.projectId" placeholder="请选择项目" :options="projectOptions" />
        </n-form-item>
        <n-form-item path="purposeDetail" label="详细用途">
            <n-input v-model:value="model.purposeDetail" placeholder="请输入详细用途" />
        </n-form-item>
        <n-form-item path="counterpartyAccountNumber" label="对方账号">
            <n-input v-model:value="model.counterpartyAccountNumber" placeholder="请输入对方账号" />
        </n-form-item>
        <n-form-item path="counterpartyBankName" label="对方开户行">
            <n-input v-model:value="model.counterpartyBankName" placeholder="请输入对方开户行" />
        </n-form-item>

    </n-form>
</template>
<script setup lang="ts">
    import { ref, computed } from "vue";
    import { FygDataSource } from ".";
    import type { FormInst, FormItemInst, FormItemRule, FormRules } from 'naive-ui'
    import { useMessage } from 'naive-ui'
    import type { SelectGroupOption, SelectOption, } from 'naive-ui'

    // 默认选项
    const bankOptions = [
        { label: '中国银行椒江支行', value: 1 },
        { label: '兴业银行台州分行', value: 2 },
        { label: '兴业银行台州分行', value: 3 },
    ]

    const counterpartyOptions = [{ label: '方远房地产集团有限公司', value: 1 }, { label: '台州市路桥区路南街道方林村经济合作社', value: 2 }]

    const projectOptions = [
        { label: '合璟府', value: 101 },
        { label: '天合府', value: 102 },
    ]

    const fundDailyDimensionOptions: Array<SelectOption | SelectGroupOption> = [
        {
            type: 'group',
            label: '收入',
            key: '收入',
            children: [
                { label: '销售收入', value: 1 },
                { label: '其他收入', value: 2 },
                { label: '资金流入-股东方', value: 3 },
                { label: '资金流入-内部单位', value: 4 },
                { label: '资金流入-外部单位', value: 5 },
                { label: '融资流入', value: 6 }
            ]
        },
        {
            type: 'group',
            label: '支出',
            key: '支出',
            children: [
                { label: '土地支出', value: 7 },
                { label: '工程支出', value: 8 },
                { label: '税金支出', value: 9 },
                { label: '销售费用', value: 10 },
                { label: '管理费用', value: 11 },
                { label: '财务费用', value: 12 },
                { label: '其他支出', value: 13 },
                { label: '资金流出-股东方', value: 14 },
                { label: '资金流出-内部单位', value: 15 },
                { label: '资金流出-外部单位', value: 16 },
                { label: '融资还贷支出', value: 17 },
                { label: '融资费用', value: 18 }
            ]
        }];

    const relevantValues = [3, 4, 5, 6, 14, 15, 16, 17];
    const taxDetailRequiredValue = 9;
    const developmentCostDetailRequiredValue = 8;

    const isRelevantCounterparty = computed(() => relevantValues.includes(model.fundDailyDimensionId ?? 0));
    const isTaxDetailRequired = computed(() => model.fundDailyDimensionId === taxDetailRequiredValue);
    const isDevelopmentCostDetailRequired = computed(() => model.fundDailyDimensionId === developmentCostDetailRequiredValue);
    const taxDetailOptions = [
        { label: '个人所得税', value: '个人所得税' },
        { label: '印花税', value: '印花税' },
        { label: '增值税', value: '增值税' },
        { label: '城建税', value: '城建税' },
        { label: '教育费附加', value: '教育费附加' },
        { label: '地方教育附加', value: '地方教育附加' },
        { label: '土增税', value: '土增税' },
        { label: '企业所得税', value: '企业所得税' },
        { label: '土地使用税', value: '土地使用税' },
        { label: '其他', value: '其他' }
    ];

    const developmentCostDetailOptions = [
        { label: '建安工程费', value: '建安工程费' },
        { label: '前期工程费', value: '前期工程费' },
        { label: '开发间接费', value: '开发间接费' },
        { label: '公共配套设施费', value: '公共配套设施费' },
        { label: '基础设施费', value: '基础设施费' },
        { label: '其他', value: '其他' }];


    interface Props {
        width: number
    }
    const props = withDefaults(defineProps<Props>(), {
        width: 300,
    })

    interface FormStyle {
        width: string
    }

    const formStyle = reactive<FormStyle>({
        width: props.width + 'px'
    })

    const formRef = ref<FormInst | null>(null)
    const message = useMessage()
    const model = reactive<FygDataSource>({
        projectId: 101,
    })

    type ruleType = 'string' | 'number' | 'boolean' | 'array' | 'date' | 'email' | 'url'

    const requiredValidatorInput = (fieldName: string, type?: ruleType) => ({
        required: true,
        type: type,
        message: `请输入${fieldName}`,
        trigger: ['blur', 'input']
    });

    const requiredValidatorSelect = (fieldName: string, type?: ruleType) => ({
        required: true,
        type: type,
        message: `请选择${fieldName}`,
        trigger: ['blur', 'change'],
    });


    const rules: FormRules = {
    counterpartyAccountId: requiredValidatorSelect('对方户名', 'number'),
    taxDetail: requiredValidatorSelect('税金明细', 'string'),
    annualInterestRate: requiredValidatorInput('年利率', 'number'),
    developmentCostDetail: requiredValidatorSelect('开发成本明细', 'string'),
    // 其他自定义的规则
    fundDailyDimensionId: [
        {
            validator: (rule, value) => {
                if (value === developmentCostDetailRequiredValue && !model.developmentCostDetail) {
                    return new Error('请选择开发成本明细');
                }
                return true;  // 返回 true 明确表示验证通过
            },
            trigger: ['blur', 'change']
        },
        {
            validator: (rule, value) => {
                if (value === taxDetailRequiredValue && !model.taxDetail) {
                    return new Error('请选择税金明细');
                }
                return true;
            },
            trigger: ['blur', 'change']
        },
        {
            validator: (rule, value) => {
                if (relevantValues.includes(value) && !model.counterpartyAccountId) {
                    return new Error('请选择对方户名');
                }
                return true;
            },
            type: 'number',
            trigger: ['blur', 'change']
        },
        {
            validator: (rule, value) => {
                if (value === 6 && !model.annualInterestRate) {
                    return new Error('请输入年利率');
                }
                return true;
            },
            type: 'number',
            trigger: ['blur', 'change']
        },
        requiredValidatorSelect('资金日报维度', 'number'),
    ],
};



    async function handleValidateButtonClick() {

        // 使用 Promise 封装 validate 的回调
        return new Promise((resolve, reject) => {
            formRef.value?.validate((errors) => {
                if (!errors) {
                    message.success('验证成功');
                    resolve(1); // 验证成功，解决 Promise
                } else {
                    console.log(errors);
                    message.error('验证失败');
                    reject(errors); // 验证失败，拒绝 Promise
                }
            });
        });
    }
    const isSubmitting = ref(false)

    async function saveData() {
        await handleValidateButtonClick(); // 等待验证结果
        await new Promise((resolve) => setTimeout(resolve, 1000));
        message.success('保存成功');
    }

    async function doSave(e: MouseEvent) {
        e.preventDefault(); // 防止默认行为，如提交表单等
        isSubmitting.value = true;

        try {
            await saveData();
        } catch (errors) {
            console.error('保存失败，因为验证未通过：', errors);
        } finally {
            isSubmitting.value = false; // 无论成功还是失败，都重置提交状态
        }
    }

    defineExpose({ doSave, })

    watch(() => props.width, (newWidth) => {
        formStyle.width = newWidth - 30 + 'px'
    })
</script>