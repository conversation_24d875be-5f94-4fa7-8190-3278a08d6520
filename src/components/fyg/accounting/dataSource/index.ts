type FygDataSource = {
    id?: number; // 主键
    transactionDate?: number; // 交易日期
    bankAccountId?: number; // 银行账户ID，关联 bank_accounts 表
    bankAccountName?: string; // 银行账户名称
    bankAccountNumber?: string; // 银行账户账号
    bankAbbreviation?: string; // 银行账户简称
    incomeAmount?: number; // 收入金额
    expenseAmount?: number; // 支出金额
    counterpartyAccountId?: number; // 对方户名ID，关联 counterparty_accounts 表 
    counterpartyName?: string; // 对方名称
    counterpartyType?: string; // 对方类型 
    fundDailyDimensionId?: number; // 资金日报维度ID，关联 fund_daily_dimension 表
    taxDetail?: string; // 税金明细
    developmentCostDetail?: string; // 开发成本明细
    projectId?: number; // 项目ID，关联 projects 表
    purposeDetail?: string; // 详细用途
    counterpartyAccountNumber?: string; // 对方账号
    counterpartyBankName?: string; // 对方开户行
    annualInterestRate?: number; // 年利率，百分比
    dataStatus?: number; // 数据状态，1表示正常，0表示删除
    createdAt?: number; // 创建时间
    updatedAt?: number; // 更新时间
    createdBy?: string; // 创建者
    updatedBy?: string; // 最后编辑者
    secondaryDetail?: string; // 二级明细
    tertiaryDetail?: string; // 三级明细
    remark?: string; // 备注
    tempId?: string; // 临时ID
}
const fundDailyDimensionOptions = [{ label: '销售收入', value: 1 },
{ label: '其他收入', value: 2 },
{ label: '资金流入-股东方', value: 3 },
{ label: '资金流入-内部单位', value: 4 },
{ label: '资金流入-外部单位', value: 5 },
{ label: '融资流入', value: 6 }, { label: '土地支出', value: 7 },
{ label: '工程支出', value: 8 },
{ label: '税金支出', value: 9 },
{ label: '销售费用', value: 10 },
{ label: '管理费用', value: 11 },
{ label: '财务费用', value: 12 },
{ label: '其他支出', value: 13 },
{ label: '资金流出-股东方', value: 14 },
{ label: '资金流出-内部单位', value: 15 },
{ label: '资金流出-外部单位', value: 16 },
{ label: '融资还贷支出', value: 17 },
{ label: '融资费用', value: 18 }]

const counterpartyTypeOptions = [
    { label: '空值', value: '' },
    { label: '股东方', value: 'GU_DONG_FANG' },
    { label: '内部单位', value: 'NEI_BU_DAN_WEI' },
    { label: '外部单位', value: 'WAI_BU_DAN_WEI' },
    { label: '贷款银行', value: 'DAI_KUAN_YIN_HANG' }
];

const bankAccountTypeOptions = [{ label: '基本户', value: 'JI_BEN_HU' },
{ label: '监管户', value: 'JIAN_GUAN_HU' },
{ label: '一般户', value: 'YI_BAN_HU' },
{ label: '贷款户', value: 'DAI_KUAN_HU' }
];

const dimensionTypeOptions = [{ label: '收入', value: 'REVENUE' }, { label: '支出', value: 'EXPENSE' }];

const equityTypeOptions = [{ label: '明股', value: 'MING_GU' }, { label: '暗股', value: 'AN_GU' }];

const optionsMap = {
    'fundDailyDimensionId': fundDailyDimensionOptions,
    'counterpartyType': counterpartyTypeOptions,
    'bankAccountType': bankAccountTypeOptions,
    'dimensionType': dimensionTypeOptions,
    'equityType': equityTypeOptions,
};

type QueryParam = {
    dataStatus?: number,
    projectName?: string,
    currentPage?: number,
    pageSize?: number,
    order?: string,
    columnKey?: string,
    projectId?: number,
    [key: string]: any
}
export { optionsMap, fundDailyDimensionOptions, counterpartyTypeOptions, bankAccountTypeOptions, dimensionTypeOptions, equityTypeOptions };
export type { FygDataSource, QueryParam };
