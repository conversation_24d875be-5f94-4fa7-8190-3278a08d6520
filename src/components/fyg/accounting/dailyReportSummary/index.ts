// 基础类型，适用于所有共有的字段
export interface DailyReportSummaryBase {
    id?: number; // 主键ID
    projectId?: number; // 项目ID，关联项目表
    sequenceNumber?: number; // 序号
    projectName?: string; // 项目名称
    repaymentPlan?: string; // 还贷计划（时间或条件）
    remarks?: string; // 备注信息
    dataStatus?: number; // 数据状态，1表示正常，0表示删除
    createdBy?: string; // 创建者
    updatedBy?: string; // 最后更新者
    editable?: boolean; // 是否可编辑
    [key: string]: any;
}

// DTO 类型，扩展基础类型，包含更多字段
export interface DailyReportSummaryDTO extends DailyReportSummaryBase {
    pureEquityRatio?: number; // 纯股比，百分比
    realEstateInput?: number; // 房产投入金额
    realEstateRecovered?: number; // 房产回款金额
    actualUsedRealEstateFunds?: number; // 实际占用房产资金
    partnerFundsUsed?: number; // 合作股东占用资金
    fundBalance?: number; // 资金余额
    supervisedAccountBalance?: number; // 监管户余额
    loanBalance?: number; // 贷款余额
    dueDate?: number; // 到期日（时间戳）
    saleValueWithoutBuyback?: number; // 销售货值（不含回购）
    saleAmountWithoutBuyback?: number; // 销售金额（不含回购）
    receivedSalesFundsWithoutBuyback?: number; // 已收销售款（不含回购）
    recoverableFundsWithoutBuyback?: number; // 已售可收（不含回购）
    buybackValue?: number; // 回购货值
    reachedBuybackNode?: number; // 节点已到回购（类销售金额）
    receivedBuybackFunds?: number; // 已收回购款
    recoverableBuybackFunds?: number; // 可收回购款
    operatingExpenditure?: number; // 经营支出
}

// Query 类型，扩展基础类型，字段均为 JSON 字符串形式，用于传递过滤条件
export interface DailyReportSummaryQuery extends DailyReportSummaryBase {
    pureEquityRatioJsonStr?: string; // 纯股比，百分比（JSON字符串）
    realEstateInputJsonStr?: string; // 房产投入金额（JSON字符串）
    realEstateRecoveredJsonStr?: string; // 房产回款金额（JSON字符串）
    actualUsedRealEstateFundsJsonStr?: string; // 实际占用房产资金（JSON字符串）
    partnerFundsUsedJsonStr?: string; // 合作股东占用资金（JSON字符串）
    fundBalanceJsonStr?: string; // 资金余额（JSON字符串）
    supervisedAccountBalanceJsonStr?: string; // 监管户余额（JSON字符串）
    loanBalanceJsonStr?: string; // 贷款余额（JSON字符串）
    dueDateJsonStr?: string; // 到期日（JSON字符串）
    saleValueWithoutBuybackJsonStr?: string; // 销售货值（不含回购）（JSON字符串）
    saleAmountWithoutBuybackJsonStr?: string; // 销售金额（不含回购）（JSON字符串）
    receivedSalesFundsWithoutBuybackJsonStr?: string; // 已收销售款（不含回购）（JSON字符串）
    recoverableFundsWithoutBuybackJsonStr?: string; // 已售可收（不含回购）（JSON字符串）
    buybackValueJsonStr?: string; // 回购货值（JSON字符串）
    reachedBuybackNodeJsonStr?: string; // 节点已到回购（类销售金额）（JSON字符串）
    receivedBuybackFundsJsonStr?: string; // 已收回购款（JSON字符串）
    recoverableBuybackFundsJsonStr?: string; // 可收回购款（JSON字符串）
    operatingExpenditureJsonStr?: string; // 经营支出（JSON字符串）
}
