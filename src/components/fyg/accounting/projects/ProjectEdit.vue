<template>
    <n-form ref="formRef" :model="model" :rules="rules" :style="formStyle">
        <n-form-item path="company" label="单位">
            <n-input v-model:value="model.company" @keydown.enter.prevent />
        </n-form-item>
        <n-form-item path="projectName" label="项目名称">
            <n-input v-model:value="model.projectName" @keydown.enter.prevent />
        </n-form-item>
        <n-form-item path="accountant" label="项目财务">
            <n-input v-model:value="model.accountant" @keydown.enter.prevent />
        </n-form-item>
        <n-form-item ref="rPasswordFormItemRef" first label="项目位置">
            <n-input v-model:value="model.projectLocation" @keydown.enter.prevent />
        </n-form-item>
        <n-form-item label="项目类型">
            <n-select v-model:value="model.projectType" :options="projectTypeOptions" />
        </n-form-item>
        <n-form-item label="拿地时间">
            <n-date-picker v-model:value="model.landAcquisitionDate" type="month" format="y年 M月" year-format="y年"
                month-format="M月" clearable />
        </n-form-item>
        <n-form-item label="开盘时间">
            <n-date-picker v-model:value="model.openingDate" type="month" format="y年 M月" year-format="y年"
                month-format="M月" clearable />
        </n-form-item>
        <n-form-item label="竣备时间">
            <n-date-picker v-model:value="model.completionPreparationDate" type="month" format="y年 M月" year-format="y年"
                month-format="M月" clearable />
        </n-form-item>
        <n-form-item label="交房时间">
            <n-date-picker v-model:value="model.handoverDate" type="month" format="y年 M月" year-format="y年"
                month-format="M月" clearable />
        </n-form-item>
        <n-form-item label="土地出让金(合同金额)">
            <n-input-number v-model:value="model.landTransferFee" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="土地面积">
            <n-input-number v-model:value="model.landArea" :show-button="false">
                <template #suffix>
                    ㎡
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="总建筑面积">
            <n-input-number v-model:value="model.totalConstructionArea" :show-button="false">
                <template #suffix>
                    ㎡
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="总投">
            <n-input-number v-model:value="model.totalInvestment" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="已投入资金">
            <n-input-number v-model:value="model.fundsInvested" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="项目贷款余额">
            <n-input-number v-model:value="model.projectLoanBalance" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="可售面积">
            <n-input-number v-model:value="model.sellableArea" :show-button="false">
                <template #suffix>
                    ㎡
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="已售面积">
            <n-input-number v-model:value="model.soldArea" :show-button="false">
                <template #suffix>
                    ㎡
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="预计总货值">
            <n-input-number v-model:value="model.estimatedTotalValue" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="销售金额">
            <n-input-number v-model:value="model.salesAmount" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="回购金额">
            <n-input-number v-model:value="model.buybackAmount" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="已回笼资金">
            <n-input-number v-model:value="model.fundsRecovered" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="工程进度">
            <n-input v-model:value="model.projectProgress" :show-button="false">
            </n-input>
        </n-form-item>
        <n-form-item label="可回笼资金">
            <n-input-number v-model:value="model.recoverableFunds" :show-button="false">
                <template #suffix>
                    元
                </template>
            </n-input-number>
        </n-form-item>
        <n-form-item label="方远实际股比">
            <n-input-number v-model:value="model.fangyuanShareRatio" :show-button="false">
                <template #suffix>
                    %
                </template>
            </n-input-number>
        </n-form-item>
    </n-form>
</template>

<script setup lang="ts">
    import { fetchDataAlt } from '../../../../utils/APIUtils';
    import { doPutAsync } from '../../../../utils/AxiosUtil';
    import type { Project } from '.';
    import { projectTypeOptions } from '.'
    import { ref, reactive } from 'vue'
    import type { FormInst, FormItemInst, FormItemRule, FormRules } from 'naive-ui'
    import { useMessage } from 'naive-ui'
    const BASE_SERVER = '/fyschedule/fyg/accounting/project/';

    interface Props {
        projectId: number,
        editor: string,
        width: number
    }
    const props = withDefaults(defineProps<Props>(), {
        width: 300,
    })

    if (props.projectId > 0) {
        getData(props.projectId);
    }

    interface FormStyle {
        width: string
    }

    const formStyle = reactive<FormStyle>({
        width: props.width + 'px'
    });

    const formRef = ref<FormInst | null>(null)
    const rPasswordFormItemRef = ref<FormItemInst | null>(null)
    const message = useMessage()
    const model = reactive<Project>({
        createdBy: props.editor,
        updatedBy: props.editor,
        dataStatus: 1
    })

    const rules: FormRules = {
        company: [{ required: true, message: '请输入单位', trigger: ['input', 'blur'] }],
        projectName: [{ required: true, message: '请输入项目名称', trigger: ['input', 'blur'] }],
        accountant: [{ required: true, message: '请输入项目财务', trigger: ['input', 'blur'] }],
        age: [
            {
                required: true,
                validator(rule: FormItemRule, value: string) {
                    if (!value) {
                        return new Error('需要年龄')
                    }
                    else if (!/^\d*$/.test(value)) {
                        return new Error('年龄应该为整数')
                    }
                    else if (Number(value) < 18) {
                        return new Error('年龄应该超过十八岁')
                    }
                    return true
                },
                trigger: ['input', 'blur']
            }
        ],
    }

    async function handleValidateButtonClick() {

        // 使用 Promise 封装 validate 的回调
        return new Promise((resolve, reject) => {
            formRef.value?.validate((errors) => {
                if (!errors) {
                    message.success('验证成功');
                    resolve(1); // 验证成功，解决 Promise
                } else {
                    console.log(errors);
                    message.error('验证失败');
                    reject(errors); // 验证失败，拒绝 Promise
                }
            });
        });
    }

    const isSubmitting = ref(false)
    async function doSave(e: MouseEvent) {
        e.preventDefault(); // 防止默认行为，如提交表单等
        isSubmitting.value = true

        try {
            // 先进行表单验证
            await handleValidateButtonClick(); // 等待验证结果

            // 如果验证成功，则继续执行保存操作
            await saveData(); // 在这里调用saveProject函数，进行保存操作
            
        } catch (errors) {
            // 如果验证失败，则在此处处理错误，例如显示错误信息或进行其他操作
            console.error('保存失败，因为验证未通过：', errors);
        } finally {
            setTimeout(() => {
                isSubmitting.value = false; // 无论成功还是失败，都重置提交状态
            }, 1000);
        }
    }

    async function saveData() {
        const url = BASE_SERVER + 'save';
        const response: any = await doPutAsync(url, model);
        if (response.code === 200) {
            model.id = response.data.id;
            message.success(response.message)
        } else {
            message.error(`${response.message} 错误代码: ${response.code}`)
        }
    }

    async function getData(id: number) {
        const url = BASE_SERVER + 'find/id';
        const queryParam = { id: id };
        const results = await fetchDataAlt(url, queryParam);
        
        if (results.length > 0) {
            const result = results[0];
            

            Object.assign(model, {
                ...result,  // 确保从服务器返回的结果是完整的，包括所有需要的字段
                updatedBy: props.editor
            });
        }
    }

    defineExpose({ doSave, })

    const emits = defineEmits(['update:status'])
    watch(isSubmitting, (newVal) => {
        emits('update:status', newVal);
    })

    watch(() => props.width, (newWidth) => {
        formStyle.width = newWidth - 30 + 'px'
    })

</script>