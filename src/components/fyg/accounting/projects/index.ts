import { idID, NButton, NGi, NGrid, NIcon, NInput, NSpace, NTag } from 'naive-ui';
import {
  DataTableBaseColumn,
  DataTableFilterState
} from 'naive-ui'
import { Search as IconSearch, } from '@vicons/ionicons5'
import { h } from 'vue';

import MyFilter from '../../../../components/MyFilterComponent.vue';
import MyDatePicker from '../../../../components/MyDateFilterComponent.vue'
import { formatDate } from '../../../../utils/xUtils';


type TableData = {
  content?: Project[],
  totalElements?: number,
  totalPages?: number
}

// 类型定义 & 封装方法
type Project = {
  id?: number;
  company?: string; // 单位
  projectName?: string; // 项目名称
  projectLocation?: string; // 项目位置
  projectType?: string; // 项目类型
  landAcquisitionDate?: number; // 拿地时间
  openingDate?: number; // 开盘时间
  completionPreparationDate?: number; // 竣备时间
  handoverDate?: number; // 交房时间
  landTransferFee?: number; // 土地出让金(合同金额)
  landArea?: number; // 土地面积
  totalConstructionArea?: number; // 总建筑面积
  totalInvestment?: number; // 总投
  fundsInvested?: number; // 已投入资金
  projectLoanBalance?: number; // 项目贷款余额
  sellableArea?: number; // 可售面积
  soldArea?: number; // 已售面积
  estimatedTotalValue?: number; // 预计总货值
  salesAmount?: number; // 销售金额
  buybackAmount?: number; // 回购金额
  fundsRecovered?: number; // 已回笼资金
  projectProgress?: string; // 工程进度
  recoverableFunds?: number; // 可回笼资金
  fangyuanShareRatio?: number; // 方远实际股比
  accountant?: string; // 项目财务
  dataStatus?: number; // 数据状态
  createdAt?: number; // 创建时间
  updatedAt?: number; // 更新时间
  createdBy?: string; // 创建者
  updatedBy?: string; // 最后编辑者
  [key: string]: any;
};

type QueryParam = {
  
  currentPage?: number,
  pageSize?: number,
  order?: string,
  columnKey?: string,
  company?: string,
  accountant?: string,
  dataStatus?: number,
  projectName?: string,
  projectLocation?: string,
  projectType?: string,
  landAcquisitionDateJsonStr?: string,
  openingDateJsonStr?: string,
  completionPreparationDateJsonStr?: string,
  handoverDateJsonStr?: string,
  landTransferFeeJsonStr?: string,
  landAreaJsonStr?: string,
  totalConstructionAreaJsonStr?: string,
  totalInvestmentJsonStr?: string,
  fundsInvestedJsonStr?: string,
  projectLoanBalanceJsonStr?: string,
  sellableAreaJsonStr?: string,
  soldAreaJsonStr?: string,
  estimatedTotalValueJsonStr?: string,
  salesAmountJsonStr?: string,
  buybackAmountJsonStr?: string,
  fundsRecoveredJsonStr?: string,
  projectProgress?: string,
  recoverableFundsJsonStr?: string,
  fangyuanShareRatioJsonStr?: string,
  projectTypeJsonStr?: string,
  [key: string]: any
}

const projectTypeOptions = [{
  label: '住宅',
  value: '住宅',
}, {
  label: '商务办公',
  value: '商务办公',
}, {
  label: '商住',
  value: '商住',
}, {
  label: '工业',
  value: '工业',
}];

const optionsMap = {
  'projectType': projectTypeOptions,
};

interface ExtendedDataTableBaseColumn extends DataTableBaseColumn {
  isActivated: boolean;
}

const createColumnWithTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
  const { key } = col as ExtendedDataTableBaseColumn;
  let column = reactive({
    ...col,
    filter: true,
    filterOptionValue: null,
    renderFilterIcon: () => {
      return h(NIcon, null, { default: () => h(IconSearch) });
    },
    renderFilterMenu: ({ hide }: { hide: any }) => {
      return type === 'text' ? h(MyFilter, {
        hide,
        type: type,
        filterOptionValue: column.filterOptionValue,
        onFilter: (val: any) => {
          column.filterOptionValue = val;

          if (doFilter) {
            doFilter({ key: key, value: val });
          }
        }
      }) : undefined;
    }
  });
  return column;
}

const createColumnWithDatePickerTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
  const { key } = col as ExtendedDataTableBaseColumn;
  let column = reactive({
    ...col,
    filter: true,
    filterOptionValue: null,
    renderFilterIcon: () => {
      return h(NIcon, null, { default: () => h(IconSearch) });
    },
    renderFilterMenu: ({ hide }: { hide: any }) => {
      return type === 'datePicker' ? h(MyDatePicker, {
        hide,
        type: type,
        filterOptionValue: column.filterOptionValue,
        onFilter: (val: any) => {
          column.filterOptionValue = val;
          if (doFilter) {
            doFilter({ key: key + 'JsonStr', value: val });
          }
        }
      }) : undefined;
    },
    render: (rowData: Project) => {
      return formatDate(rowData[key as keyof Project], 'yyyy年M月D日');
    },
  });
  return column;
}

const createColumnWithFilterOptions = (col: ExtendedDataTableBaseColumn, optionKey: keyof typeof optionsMap) => {
  let column = reactive({
    ...col,
    filter: true,
    filterMultiple: true,
    filterOptionValues: [],
    filterOptions: optionsMap[optionKey]
  });
  return column;
}

export const test = [
  // {
  //   title: '单位',
  //   key: 'company',
  // },
  // {
  //   title: '项目名称',
  //   key: 'projectName',
  // },
  // {
  //   title: '项目位置',
  //   key: 'projectLocation',
  // },
  // {
  //   title: '项目类型',
  //   key: 'projectType',
  // },
  // {
  //   title: '工程进度',
  //   key: 'projectProgress',
  //   render(row) {
  //     console.log('Rendering projectProgress for row:', row); // Debug log
  //     return h(NTag, { type: 'info' }, { default: () => `${row.projectProgress}%` });
  //   },
  // },
  // {
  //   title: '操作',
  //   key: 'actions',
  //   render(row) {
  //     console.log('Rendering actions for row:', row); // Debug log
  //     return h(
  //       NButton,
  //       {
  //         type: 'primary',
  //         onClick: () => handleEdit(row),
  //       },
  //       { default: () => '编辑' }
  //     );
  //   },
  // },
];

type SETTING = {
  key: string,
  title: string,
  isActivated: true | false
};

const DEFAULT_SETTING: SETTING[] = [
  { key: 'projectName', title: '项目名称', isActivated: true },
  { key: 'projectType', title: '承包类型', isActivated: true },
  { key: 'openDate', title: '开盘时间', isActivated: true },
  { key: 'company', title: '单位', isActivated: true },
  { key: 'projectLocation', title: '项目位置', isActivated: true },
  { key: 'landAcquisitionDate', title: '拿地时间', isActivated: true },
  { key: 'openingDate', title: '开盘时间', isActivated: true },
  { key: 'completionPreparationDate', title: '竣备时间', isActivated: true },
  { key: 'handoverDate', title: '交房时间', isActivated: true },
  { key: 'landTransferFee', title: '土地出让金(合同金额)', isActivated: true },
  { key: 'landArea', title: '土地面积', isActivated: true },
  { key: 'totalConstructionArea', title: '总建筑面积', isActivated: true },
  { key: 'totalInvestment', title: '总投', isActivated: true },
  { key: 'fundsInvested', title: '已投入资金', isActivated: true },
  { key: 'projectLoanBalance', title: '项目贷款余额', isActivated: true },
  { key: 'sellableArea', title: '可售面积', isActivated: true },
  { key: 'soldArea', title: '已售面积', isActivated: true },
  { key: 'estimatedTotalValue', title: '预计总货值', isActivated: true },
  { key: 'salesAmount', title: '销售金额', isActivated: true },
  { key: 'buybackAmount', title: '回购金额', isActivated: true },
  { key: 'fundsRecovered', title: '已回笼资金', isActivated: true },
  { key: 'projectProgress', title: '工程进度', isActivated: true },
  { key: 'recoverableFunds', title: '可回笼资金', isActivated: true },
  { key: 'fangyuanShareRatio', title: '方远实际股比', isActivated: true },
  { key: 'accountant', title: '项目财务', isActivated: true }
];

export const handleEdit = (row: any) => {
  console.log('handleEdit called with row:', row); // Debug log
  // 跳转到编辑页面并填充数据
  window.location.href = `/edit-project?id=${row.id}`;
};

export const fetchProjects = async () => {
  console.log('fetchProjects called'); // Debug log
  // 从后端获取项目列表数据
  return [
    {
      id: 101,
      company: '方远房地产集团有限公司',
      projectName: '合璟府',
      projectLocation: '台州市中山西路北侧、学院路东侧（地块一）及中山西路南侧、葭西路东侧（地块二）',
      projectType: '商住',
      projectProgress: 75,
      landAcquisitionDate: new Date('2024-12-11').getTime(),
    },
    {
      id: 102,
      company: '方远房地产集团有限公司',
      projectName: '天合府',
      projectLocation: '中央商务区天盛中心南侧',
      projectType: '商住',
      projectProgress: 100,
    },
    {
      id: 103,
      company: '台州椒江方远荣安置业有限公司',
      projectName: '海尚望府',
      projectLocation: '椒江区白云山南路以东、洪家北环线以南地块',
      projectType: '住宅',
      projectProgress: 50,
    },
    
  ];
};

export type { Project, QueryParam, TableData, SETTING, ExtendedDataTableBaseColumn };

export {
  projectTypeOptions, DEFAULT_SETTING, optionsMap,
  createColumnWithDatePickerTemplate, createColumnWithFilterOptions, createColumnWithTemplate,
};