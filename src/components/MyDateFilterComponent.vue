<template>
    <div class="filter-container" v-if="type === 'datePicker'">
        <n-space vertical>
            <n-space vertical :align="'center'" :size="'small'">
                <n-date-picker v-model:value="datePickerValue.startDate"
                    @update-value="setDatePickerValue($event, 'startDate')" type="date" placeholder="选择开始时间" />
                至
                <n-date-picker v-model:value="datePickerValue.stopDate"
                    @update-value="setDatePickerValue($event, 'stopDate')" type="date" placeholder="选择结束时间" />

            </n-space>
            <n-grid :x-gap="80" :cols="2">
                <n-gi>
                    <n-button :size="'small'" @click="clearFilter">清除</n-button>
                </n-gi>
                <n-gi>
                    <n-button :size="'small'" :type="'primary'" @click="applyFilter">查询</n-button>
                </n-gi>
            </n-grid>
        </n-space>
    </div>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { InternalValue } from '../fconfig/fyjs/project/management/index'
interface Props {
    filterOptionValue: any,
    type?: keyof InternalValue,
    hide?: Function,
    onFilter?: Function
}

const props = withDefaults(defineProps<Props>(), {});
const type = ref(props.type);
const internalValue = ref(props.filterOptionValue);
// 初始化 datePickerValue
const initDateValues = (filterOptionValue: any) => {
    let startDate = null;
    let stopDate = null;
    if (Array.isArray(filterOptionValue) && filterOptionValue.length === 2) {
        startDate = filterOptionValue[0] !== null ? Number(filterOptionValue[0]) : null;
        stopDate = filterOptionValue[1] !== null ? Number(filterOptionValue[1]) : null;
    }
    return { startDate, stopDate };
};

const initialDateValues = initDateValues(props.filterOptionValue);

const datePickerValue = reactive({
    startDate: initialDateValues.startDate,
    stopDate: initialDateValues.stopDate
});

const updateInternalValue = () => {
    const start = datePickerValue.startDate !== null ? String(datePickerValue.startDate) : null;
    const stop = datePickerValue.stopDate !== null ? String(datePickerValue.stopDate) : null;
    internalValue.value = [start, stop]; 
    console.log(`Updated internalValue:`, [start, stop]);
};
const setDatePickerValue = (e: number | null, key: keyof typeof datePickerValue) => {
    datePickerValue[key] = e;
    updateInternalValue();
};
// Watch for external value changes
watch(() => props.filterOptionValue, (newValue) => {
    console.log(`output->newValue`,newValue)
    internalValue.value = newValue;
    if (Array.isArray(newValue) && newValue.length === 2) {
        // 解构数组，可能包含 undefined 值
        const [startDate, stopDate] = newValue;
        // 分别更新 startDate 和 stopDate，如果为 undefined 则不更新
        datePickerValue.startDate = Number(startDate) ?? datePickerValue.startDate;
        datePickerValue.stopDate = Number(stopDate) ?? datePickerValue.stopDate;
        console.log(`output->datePickerValue`,datePickerValue)
    } else {
        // 如果 newFilterOptionValue 为 null 或其他非预期值，可选择重置日期
        datePickerValue.startDate = null;
        datePickerValue.stopDate = null;
    }
});

// Emit the filter value when apply is clicked or input is blurred
function applyFilter() {
    console.log(`output->internalValue.value`,internalValue.value )
    props.onFilter?.(internalValue.value ? [datePickerValue.startDate, datePickerValue.stopDate] : internalValue.value);
    props.hide?.();
}

// Clear the filter
function clearFilter() {
    internalValue.value = null;
    datePickerValue.startDate = null;
    datePickerValue.stopDate = null;
    applyFilter();  // Apply filter after clearing to update external components

}
</script>
<style scoped>
.filter-container {
    padding: 8px;
}
</style>