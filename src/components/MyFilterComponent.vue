<template>
    <div class="filter-container" v-if="type === 'text'">
        <n-space vertical>
            <n-input v-model:value="internalValue" placeholder='Enter text to filter' />
            <n-grid :x-gap="80" :cols="2">
                <n-gi>
                    <n-button :size="'small'" @click="clearFilter">清除</n-button>
                </n-gi>
                <n-gi>
                    <n-button :size="'small'" :type="'primary'" @click="applyFilter">查询</n-button>
                </n-gi>
            </n-grid>
        </n-space>
    </div>
</template>
<script setup lang="ts">
import { NSpace, NInput, NGrid, NGi } from 'naive-ui';
import { ref, watch } from 'vue';
import { InternalValue } from '../fconfig/fyjs/project/management/index'
interface Props {
    filterOptionValue: any,
    type?: keyof InternalValue,
    hide?: Function,
    onFilter?: Function
}

const props = withDefaults(defineProps<Props>(), {});
const type = ref(props.type);
const internalValue = ref(props.filterOptionValue);

// Watch for external value changes
watch(() => props.filterOptionValue, (newValue) => {
    internalValue.value = newValue;
});

// Emit the filter value when apply is clicked or input is blurred
function applyFilter() {
    props.onFilter?.(internalValue.value);
    props.hide?.(); // Hide the menu after applying the filter
}

// Clear the filter
function clearFilter() {
    internalValue.value = null;
    props.onFilter?.(null);
    props.hide?.(); // Hide the menu after clearing the filter
}
</script>
<style scoped>
.filter-container {
    padding: 8px;
}
</style>