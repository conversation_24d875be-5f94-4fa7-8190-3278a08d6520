<template>
    <div class="filter-container">
      <n-space vertical>
        <!-- Text input for text type filters -->
        <n-input v-if="type === 'text'" v-model="internalValue" placeholder="Enter filter value" @blur="applyFilter" />
  
        <!-- Date picker for date type filters -->
        <n-date-picker v-else-if="type === 'date'" v-model="internalValue" type="date" @blur="applyFilter" />
  
        <!-- Implement other types as needed -->
      </n-space>
      <n-button size="small" @click="clearFilter">清除</n-button>
      <n-button size="small" type="primary" @click="applyFilter">应用</n-button>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, watch } from 'vue';
  import { NInput, NButton, NSpace, NDatePicker } from 'naive-ui';
  
  // Props
  const props = defineProps({
    type: String,
    value: [String, Number, Date],
    hide: Function,
    onFilter: Function
  });
  
  // Internal value for the input
  const internalValue = ref(props.value);
  
  // Watch for external value changes
  watch(() => props.value, (newValue) => {
    internalValue.value = newValue;
  });
  
  // Emit the filter value when apply is clicked or input is blurred
  function applyFilter() {
    props.onFilter?.(internalValue.value);
    props.hide?.(); // Hide the menu after applying the filter
  }
  
  // Clear the filter
  function clearFilter() {
    internalValue.value = undefined;
    props.onFilter?.(undefined);
    props.hide?.(); // Hide the menu after clearing the filter
  }
  </script>
  
  <style scoped>
  .filter-container {
    padding: 8px;
  }
  </style>
  