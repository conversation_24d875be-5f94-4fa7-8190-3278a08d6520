<template>
    <div class="filter-container" v-if="type === 'numberPicker'">
        <n-space vertical>
            <n-space vertical :align="'center'" :size="'small'">
                <n-input-number v-model:value="numberPickerValue.startNumber" placeholder="输入最小值" :show-button="false"
                    @update:value="setNumberPickerValue($event, 'startNumber')" />
                至
                <n-input-number v-model:value="numberPickerValue.stopNumber" placeholder="输入最大值" :show-button="false"
                    @update:value="setNumberPickerValue($event, 'stopNumber')" />

            </n-space>
            <n-grid :x-gap="80" :cols="2">
                <n-gi>
                    <n-button :size="'small'" @click="clearFilter">清除</n-button>
                </n-gi>
                <n-gi>
                    <n-button :size="'small'" :type="'primary'" @click="applyFilter">查询</n-button>
                </n-gi>
            </n-grid>
        </n-space>
    </div>
</template>
<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { InternalValue } from '../fconfig/fyjs/project/management/index'
    interface Props {
        filterOptionValue: any,
        type?: keyof InternalValue,
        hide?: Function,
        onFilter?: Function
    }

    const props = withDefaults(defineProps<Props>(), {});
    const type = ref(props.type);
    const internalValue = ref(props.filterOptionValue);
    // 初始化 numberPickerValue
    const initNumberValues = (filterOptionValue: any) => {
        let startNumber = null;
        let stopNumber = null;
        if (Array.isArray(filterOptionValue) && filterOptionValue.length === 2) {
            startNumber = filterOptionValue[0] !== null ? Number(filterOptionValue[0]) : null;
            stopNumber = filterOptionValue[1] !== null ? Number(filterOptionValue[1]) : null;
        }
        return { startNumber, stopNumber };
    };

    const initialNumberValues = initNumberValues(props.filterOptionValue);

    const numberPickerValue = reactive({
        startNumber: initialNumberValues.startNumber,
        stopNumber: initialNumberValues.stopNumber
    });

    const updateInternalValue = () => {
        const start = numberPickerValue.startNumber !== null ? String(numberPickerValue.startNumber) : null;
        const stop = numberPickerValue.stopNumber !== null ? String(numberPickerValue.stopNumber) : null;
        internalValue.value = [start, stop];
        console.log(`Updated internalValue:`, [start, stop]);
    };
    const setNumberPickerValue = (e: number | null, key: keyof typeof numberPickerValue) => {
        numberPickerValue[key] = e;
        updateInternalValue();
    };
    // Watch for external value changes
    watch(() => props.filterOptionValue, (newValue) => {
        console.log(`output->newValue`, newValue)
        internalValue.value = newValue;
        if (Array.isArray(newValue) && newValue.length === 2) {
            // 解构数组，可能包含 undefined 值
            const [startNumber, stopNumber] = newValue;
            // 分别更新 startDate 和 stopDate，如果为 undefined 则不更新
            numberPickerValue.startNumber = Number(startNumber) ?? numberPickerValue.startNumber;
            numberPickerValue.stopNumber = Number(stopNumber) ?? numberPickerValue.stopNumber;
            console.log(`output->numberPickerValue`, numberPickerValue)
        } else {
            // 如果 newFilterOptionValue 为 null 或其他非预期值，可选择重置日期
            numberPickerValue.startNumber = null;
            numberPickerValue.stopNumber = null;
        }
    });

    // Emit the filter value when apply is clicked or input is blurred
    function applyFilter() {
        console.log(`output->internalValue.value`, internalValue.value)
        props.onFilter?.(internalValue.value ? [numberPickerValue.startNumber, numberPickerValue.stopNumber] : internalValue.value);
        props.hide?.();
    }

    // Clear the filter
    function clearFilter() {
        internalValue.value = null;
        numberPickerValue.startNumber = null;
        numberPickerValue.stopNumber = null;
        applyFilter();  // Apply filter after clearing to update external components

    }
</script>
<style scoped>
    .filter-container {
        padding: 8px;
    }
</style>