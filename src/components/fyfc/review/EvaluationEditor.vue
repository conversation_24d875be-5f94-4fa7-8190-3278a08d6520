<template>
  <n-card :title="cardTitle" size="small" :bordered="true" class="evaluation-card">
    <!-- 基本信息区域 -->
    <div class="basic-info-section">
      <n-form :model="formData" label-placement="left" label-width="80px" class="basic-form">
        <n-grid :cols="3" :x-gap="16">
          <n-form-item-gi label="部门">
            <n-input
              v-model:value="formData.department"
              placeholder="请输入部门"
              :disabled="!canEdit"
            />
          </n-form-item-gi>
          <n-form-item-gi label="姓名">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入姓名"
              :disabled="!canEdit"
            />
          </n-form-item-gi>
          <n-form-item-gi label="日期">
            <n-date-picker
              v-model:value="formData.date"
              type="date"
              placeholder="选择日期"
              :disabled="!canEdit"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </div>

    <n-divider />

    <!-- 评价标签区域 -->
    <div class="evaluation-tags-section">
      <n-space>
        <n-tag
          v-for="tag in evaluationTags"
          :key="tag.type"
          :type="tag.active ? 'primary' : 'default'"
          :bordered="false"
          checkable
          :checked="tag.active"
          @update:checked="(checked) => toggleTag(tag.type, checked)"
          size="large"
        >
          {{ tag.label }}
        </n-tag>
      </n-space>
    </div>

    <n-divider />

    <!-- 评分项目区域 -->
    <div class="scoring-section">
      <n-form :model="formData" label-placement="left" label-width="100px">
        <div class="score-item" v-for="item in scoreItems" :key="item.key">
          <n-form-item :label="item.label">
            <div class="score-input-container">
              <n-input-number
                v-model:value="formData.scores[item.key]"
                :min="0"
                :max="100"
                :step="1"
                :precision="1"
                placeholder="请输入分数"
                :disabled="!canEdit"
                style="width: 120px"
              />
              <span class="score-unit">分</span>
              <div class="score-description">
                <n-text depth="3" style="font-size: 12px">
                  {{ getScoreDescription(formData.scores[item.key]) }}
                </n-text>
              </div>
            </div>
          </n-form-item>
        </div>

        <!-- 小计 -->
        <n-divider />
        <n-form-item label="小计">
          <div class="subtotal-container">
            <n-text strong style="font-size: 18px; color: #2080f0">
              {{ (subtotal || 0).toFixed(1) }} 分
            </n-text>
            <n-text depth="3" style="margin-left: 16px">
              (平均分: {{ averageScore.toFixed(1) }}分)
            </n-text>
            <n-tag
              :type="getGradeType(averageScore)"
              style="margin-left: 16px"
              size="large"
            >
              {{ getGradeText(averageScore) }}
            </n-tag>
          </div>
        </n-form-item>
      </n-form>
    </div>

    <!-- 备注区域 -->
    <n-divider />
    <div class="remarks-section">
      <n-form-item label="备注">
        <n-input
          v-model:value="formData.remarks"
          type="textarea"
          placeholder="请输入备注信息..."
          :rows="3"
          :disabled="!canEdit"
        />
      </n-form-item>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section" v-if="showActions">
      <n-space justify="center">
        <n-button @click="handleReset" :disabled="!canEdit">
          重置
        </n-button>
        <n-button type="primary" @click="handleSave" :disabled="!canEdit">
          保存
        </n-button>
        <n-button type="info" @click="handlePreview">
          预览
        </n-button>
      </n-space>
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import {
  NCard,
  NForm,
  NFormItem,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NDatePicker,
  NTag,
  NSpace,
  NDivider,
  NText,
  NButton,
  useMessage
} from 'naive-ui';

// 类型定义
export interface EvaluationData {
  department: string;
  name: string;
  date: number | null;
  evaluationTypes: string[];
  scores: {
    performance: number | null;    // 工作业绩
    attitude: number | null;       // 工作态度
    ability: number | null;        // 工作能力
    growth: number | null;         // 个人成长
  };
  remarks: string;
}

export interface UserRole {
  type: 'employee' | 'colleague' | 'supervisor' | 'admin';
  canEdit: boolean;
  canView: boolean;
}

// Props 定义
interface Props {
  modelValue?: EvaluationData;
  userRole?: UserRole;
  title?: string;
  showActions?: boolean;
}

// Emits 定义
interface Emits {
  (e: 'update:modelValue', value: EvaluationData): void;
  (e: 'save', value: EvaluationData): void;
  (e: 'reset'): void;
  (e: 'preview', value: EvaluationData): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    department: '',
    name: '',
    date: null,
    evaluationTypes: [],
    scores: {
      performance: null,
      attitude: null,
      ability: null,
      growth: null
    },
    remarks: ''
  }),
  userRole: () => ({
    type: 'employee',
    canEdit: true,
    canView: true
  }),
  title: '方远房地产集团员工月度绩效考核评分表',
  showActions: true
});

const emit = defineEmits<Emits>();
const message = useMessage();

// 响应式数据
const formData = reactive<EvaluationData>({ ...props.modelValue });

// 计算属性
const cardTitle = computed(() => props.title);

const canEdit = computed(() => props.userRole.canEdit);

const evaluationTags = computed(() => [
  {
    type: 'self',
    label: '员工自评',
    active: formData.evaluationTypes.includes('self')
  },
  {
    type: 'colleague',
    label: '同事评价',
    active: formData.evaluationTypes.includes('colleague')
  },
  {
    type: 'supervisor',
    label: '直接主管评价',
    active: formData.evaluationTypes.includes('supervisor')
  }
]);

const scoreItems = [
  { key: 'performance' as keyof EvaluationData['scores'], label: '工作业绩' },
  { key: 'attitude' as keyof EvaluationData['scores'], label: '工作态度' },
  { key: 'ability' as keyof EvaluationData['scores'], label: '工作能力' },
  { key: 'growth' as keyof EvaluationData['scores'], label: '个人成长' }
];

// 计算小计和平均分
const subtotal = computed(() => {
  const scores = Object.values(formData.scores);
  return scores.reduce((sum, score) => (sum || 0) + (score || 0), 0);
});

const averageScore = computed(() => {
  const scores = Object.values(formData.scores).filter((score): score is number => score !== null && score > 0);
  if (scores.length === 0) return 0;
  return scores.reduce((sum, score) => sum + score, 0) / scores.length;
});

// 方法
const toggleTag = (type: string, checked: boolean) => {
  if (!canEdit.value) return;

  if (checked) {
    if (!formData.evaluationTypes.includes(type)) {
      formData.evaluationTypes.push(type);
    }
  } else {
    const index = formData.evaluationTypes.indexOf(type);
    if (index > -1) {
      formData.evaluationTypes.splice(index, 1);
    }
  }
};

const getScoreDescription = (score: number | null): string => {
  if (score === null) return '';
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '合格';
  if (score >= 60) return '待改进';
  return '不合格';
};

const getGradeType = (score: number): 'success' | 'warning' | 'error' | 'info' => {
  if (score >= 90) return 'success';
  if (score >= 80) return 'info';
  if (score >= 70) return 'warning';
  return 'error';
};

const getGradeText = (score: number): string => {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '合格';
  if (score >= 60) return '待改进';
  return '不合格';
};

const handleSave = () => {
  // 验证必填项
  if (!formData.department || !formData.name || !formData.date) {
    message.error('请填写完整的基本信息');
    return;
  }

  if (formData.evaluationTypes.length === 0) {
    message.error('请至少选择一种评价类型');
    return;
  }

  const hasValidScore = Object.values(formData.scores).some(score => score !== null && score > 0);
  if (!hasValidScore) {
    message.error('请至少填写一项评分');
    return;
  }

  emit('save', { ...formData });
  message.success('保存成功');
};

const handleReset = () => {
  Object.assign(formData, {
    department: '',
    name: '',
    date: null,
    evaluationTypes: [],
    scores: {
      performance: null,
      attitude: null,
      ability: null,
      growth: null
    },
    remarks: ''
  });
  emit('reset');
  message.info('已重置表单');
};

const handlePreview = () => {
  emit('preview', { ...formData });
};

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue);
}, { deep: true });

// 监听表单数据变化，向上传递
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });
</script>

<style scoped>
.evaluation-card {
  max-width: 100%;
  margin: 0 auto;
}

.basic-info-section {
  margin-bottom: 16px;
}

.basic-form {
  width: 100%;
}

.evaluation-tags-section {
  margin: 16px 0;
}

.scoring-section {
  margin: 16px 0;
}

.score-item {
  margin-bottom: 16px;
}

.score-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-unit {
  color: #666;
  font-size: 14px;
}

.score-description {
  min-width: 60px;
}

.subtotal-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.remarks-section {
  margin: 16px 0;
}

.action-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .score-input-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .subtotal-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 标签样式优化 */
:deep(.n-tag) {
  cursor: pointer;
  transition: all 0.3s ease;
}

:deep(.n-tag:hover) {
  transform: translateY(-1px);
}

/* 表单项样式优化 */
:deep(.n-form-item-label) {
  font-weight: 500;
}

/* 输入框样式优化 */
:deep(.n-input-number) {
  text-align: center;
}
</style>