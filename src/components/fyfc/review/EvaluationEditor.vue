<template>
    <n-card :title="cardTitle" size="small" :bordered="true" class="evaluation-card">
        <!-- 基本信息区域 -->
        <div class="basic-info-section">
            <!-- 员工角色：可编辑表单 -->
            <n-form v-if="props.userRole?.type === 'employee'" :model="formData"
                :label-placement="isMobile ? 'top' : 'left'" :label-width="isMobile ? 'auto' : '80px'"
                class="basic-form">
                <n-grid :cols="isMobile ? 1 : 3" :x-gap="16" :y-gap="16">
                    <n-form-item-gi label="部门">
                        <n-input v-model:value="formData.department" placeholder="请输入部门" />
                    </n-form-item-gi>
                    <n-form-item-gi label="姓名">
                        <n-input v-model:value="formData.name" placeholder="请输入姓名" />
                    </n-form-item-gi>
                    <n-form-item-gi label="评价日期">
                        <n-date-picker v-model:value="formData.reviewDate" type="date" placeholder="选择评价日期"
                            style="width: 100%" />
                    </n-form-item-gi>
                </n-grid>
            </n-form>

            <!-- 其他角色：紧凑只读显示 -->
            <div v-else class="basic-info-compact">
                <div class="compact-info-row">
                    <div class="compact-info-item">
                        <span class="compact-label">部门:</span>
                        <n-text>{{ formData.department || '未填写' }}</n-text>
                    </div>
                    <span class="compact-separator">|</span>
                    <div class="compact-info-item">
                        <span class="compact-label">姓名:</span>
                        <n-text strong>{{ formData.name || '未填写' }}</n-text>
                    </div>
                    <span class="compact-separator">|</span>
                    <div class="compact-info-item">
                        <span class="compact-label">评价日期:</span>
                        <n-text>{{ formatDate(formData.reviewDate) || '未选择' }}</n-text>
                    </div>
                </div>
            </div>
        </div>

        <n-divider />

        <!-- 评价类型区域 -->
        <div class="evaluation-type-section">
            <div class="section-title">
                <n-text strong>评价类型</n-text>
            </div>
            <n-space :wrap="true" :size="[8, 8]">
                <n-tag v-for="tag in evaluationTags" :key="tag.type"
                    :type="tag.active ? 'primary' : (tag.available ? 'default' : 'info')" :bordered="false"
                    :checkable="tag.available" :checked="tag.active"
                    @update:checked="(checked) => toggleTag(tag.type, checked)" :size="isMobile ? 'medium' : 'large'"
                    :class="['evaluation-tag', { 'tag-disabled': !tag.available }]">
                    {{ tag.label }}
                    <span v-if="!tag.available" style="margin-left: 4px; opacity: 0.6;">
                        (仅查看)
                    </span>
                </n-tag>
            </n-space>
        </div>

        <n-divider />

        <!-- 评分项目区域 -->
        <div class="scoring-section">
            <div class="section-title">
                <n-text strong>评分项目</n-text>
                <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                    (总分90分: 工作业绩60分 + 工作态度10分 + 工作能力10分 + 个人成长10分)
                </n-text>
            </div>

            <!-- 评分类型切换（管理员可见） -->
            <div v-if="props.userRole?.type === 'admin'" class="score-type-tabs">
                <n-space>
                    <n-button
                        v-for="scoreType in availableScoreTypes"
                        :key="scoreType.type"
                        :type="currentScoreType === scoreType.type ? 'primary' : 'default'"
                        size="small"
                        @click="currentScoreType = scoreType.type"
                    >
                        {{ scoreType.label }}
                    </n-button>
                </n-space>
            </div>

            <div class="score-list">
                <div class="score-item-row" v-for="item in scoreItems" :key="item.key">
                    <div class="score-label-section">
                        <n-text strong>{{ item.label }}</n-text>
                        <n-text depth="3" style="font-size: 11px; margin-left: 8px">
                            ({{ item.min }}-{{ item.max }}{{ item.unit }})
                        </n-text>
                    </div>
                    <div class="score-input-section">
                        <n-input-number
                            :value="(currentScore[item.key as keyof EvaluationScore] as number) || null"
                            :min="item.min"
                            :max="item.max"
                            :step="1"
                            :precision="1"
                            :placeholder="`${item.min}-${item.max}`"
                            :disabled="!canEditCurrentScore"
                            :style="{ width: isMobile ? '100%' : '100px' }"
                            class="score-input"
                            @update:value="updateScore(item.key as keyof EvaluationScore, $event)"
                        >
                            <template #suffix>分</template>
                        </n-input-number>
                    </div>
                </div>
            </div>

            <!-- 小计区域 -->
            <div class="subtotal-compact">
                <div class="subtotal-row">
                    <span class="subtotal-label">小计：</span>
                    <n-text strong style="font-size: 16px; color: #2080f0">
                        {{ (subtotal || 0).toFixed(1) }} / {{ maxTotalScore }} 分
                    </n-text>
                    <span class="subtotal-extra">
                        ({{ averageScore.toFixed(1) }}% -
                        <n-tag :type="getGradeType(averageScore)" size="small" style="margin-left: 4px">
                            {{ getGradeText(averageScore) }}
                        </n-tag>)
                    </span>
                </div>
            </div>


        </div>

        <!-- 线上转发区域 -->
        <n-divider />
        <div class="additional-score-section">
            <div class="section-title">
                <n-text strong>线上转发</n-text>
                <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                    (0-10分，由综合办统一填写)
                </n-text>
            </div>

            <!-- 管理员角色：可编辑 -->
            <div v-if="props.userRole?.type === 'admin'" class="additional-score-edit">
                <n-form-item label="线上转发评分">
                    <div class="additional-score-input">
                        <n-input-number
                            v-model:value="formData.additionalScore"
                            :min="0"
                            :max="10"
                            :step="1"
                            :precision="1"
                            placeholder="0-10"
                            :style="{ width: isMobile ? '100%' : '120px' }"
                        >
                            <template #suffix>
                                分
                            </template>
                        </n-input-number>
                    </div>
                </n-form-item>
            </div>

            <!-- 其他角色：只读显示 -->
            <div v-else class="additional-score-readonly">
                <div class="readonly-score-item">
                    <div class="readonly-label">线上转发评分</div>
                    <div class="readonly-value">
                        <n-text strong style="font-size: 16px; color: #52c41a">
                            {{ formData.additionalScore?.toFixed(1) || '0.0' }} 分
                        </n-text>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最终得分区域 -->
        <n-divider />
        <div class="final-score-section">
            <div class="final-score-content">
                <div class="final-score-title">
                    <n-text strong style="font-size: 16px">最终得分</n-text>
                    <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                        (员工自评×10% + 同事评分×20% + 主管评分×70% + 线上转发)
                    </n-text>
                </div>
                <div class="final-score-value">
                    <n-text strong style="font-size: 20px; color: #f0a020">
                        {{ finalScore.toFixed(1) }} 分
                    </n-text>
                </div>
            </div>
        </div>

        <!-- 备注区域 -->
        <n-divider />
        <div class="remarks-section">
            <div class="section-title">
                <n-text strong>备注说明</n-text>
            </div>
            <n-input v-model:value="formData.comment" type="textarea" placeholder="请输入备注信息..." :rows="isMobile ? 4 : 3"
                :disabled="!canEdit" class="remarks-input" />
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-section" v-if="showActions">
            <n-space :justify="isMobile ? 'space-around' : 'center'" :size="isMobile ? 'small' : 'medium'"
                :wrap="isMobile">
                <n-button @click="handleReset" :disabled="!canEdit" :style="{ width: isMobile ? '100px' : 'auto' }">
                    重置
                </n-button>
                <n-button type="primary" @click="handleSave" :disabled="!canEdit"
                    :style="{ width: isMobile ? '100px' : 'auto' }">
                    保存
                </n-button>
                <n-button type="info" @click="handlePreview" :style="{ width: isMobile ? '100px' : 'auto' }">
                    预览
                </n-button>
            </n-space>
        </div>
    </n-card>
</template>

<script setup lang="ts">
    import { computed, reactive, watch, onMounted, onUnmounted, ref } from 'vue';
    import {
        NCard,
        NForm,
        NFormItemGi,
        NGrid,
        NInput,
        NInputNumber,
        NDatePicker,
        NTag,
        NSpace,
        NDivider,
        NText,
        NButton,
        useMessage
    } from 'naive-ui';
    import type { UserRole, EvaluationStatus } from '../../../fconfig/fyfc/review';

    // 类型定义 - 根据配置文件更新
    export interface EvaluationData {
        id?: number;
        department?: string;
        name?: string;
        reviewDate?: number | null;
        evaluationTypes: EvaluationStatus[];
        scores: EvaluationScore[];            // 改为数组，包含不同类型的评分
        finalScore?: number;                  // 最终得分
        comment?: string;                     // 说明
        additionalScore?: number;             // 线上转发
        status?: string;
        createdAt?: number;
        createdBy?: string;
        updatedBy?: string;
    }

    // 从配置文件导入类型
    interface EvaluationScore {
        id?: number;
        evaluationId?: number;
        evaluator?: string;
        type: UserRole;
        performanceScore?: number | null;
        attitudeScore?: number | null;
        abilityScore?: number | null;
        growthScore?: number | null;
        score?: number;
        signature?: string;
    }

    export interface UserRoleConfig {
        type: UserRole;
        canEdit: boolean;
        canView: boolean;
    }

    // Props 定义
    interface Props {
        modelValue?: EvaluationData;
        userRole?: UserRoleConfig;
        title?: string;
        showActions?: boolean;
    }

    // Emits 定义
    interface Emits {
        (e: 'update:modelValue', value: EvaluationData): void;
        (e: 'save', value: EvaluationData): void;
        (e: 'reset'): void;
        (e: 'preview', value: EvaluationData): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({
            department: '',
            name: '',
            reviewDate: null,
            evaluationTypes: [],
            scores: [],
            comment: ''
        }),
        userRole: () => ({
            type: 'employee',
            canEdit: true,
            canView: true
        }),
        title: '方远房地产集团员工月度绩效考核评分表',
        showActions: true
    });

    const emit = defineEmits<Emits>();
    const message = useMessage();

    // 移动端检测
    const isMobile = ref(false);

    const checkMobile = () => {
        isMobile.value = window.innerWidth <= 768;
    };

    onMounted(() => {
        checkMobile();
        window.addEventListener('resize', checkMobile);
    });

    onUnmounted(() => {
        window.removeEventListener('resize', checkMobile);
    });

    // 响应式数据
    const formData = reactive<EvaluationData>({ ...props.modelValue });

    // 当前选中的评分类型（用于管理员切换查看）
    const currentScoreType = ref<UserRole>(props.userRole?.type || 'employee');

    // 计算属性
    const cardTitle = computed(() => props.title);

    const canEdit = computed(() => props.userRole?.canEdit ?? true);

    // 获取当前评分对象
    const currentScore = computed(() => {
        return formData.scores.find(score => score.type === currentScoreType.value) || {
            type: currentScoreType.value,
            performanceScore: null,
            attitudeScore: null,
            abilityScore: null,
            growthScore: null
        };
    });

    // 可用的评分类型（管理员可以看到所有类型）
    const availableScoreTypes = computed(() => {
        if (props.userRole?.type === 'admin') {
            return [
                { type: 'employee' as UserRole, label: '员工自评' },
                { type: 'colleague' as UserRole, label: '同事评价' },
                { type: 'manager' as UserRole, label: '主管评分' }
            ];
        }
        return [{ type: props.userRole?.type || 'employee' as UserRole, label: '当前评分' }];
    });

    // 当前评分是否可编辑
    const canEditCurrentScore = computed(() => {
        if (props.userRole?.type === 'admin') {
            return false; // 管理员只能查看，不能编辑
        }
        return canEdit.value && currentScoreType.value === props.userRole?.type;
    });

    // 更新评分
    const updateScore = (key: keyof EvaluationScore, value: number | null) => {
        const scoreIndex = formData.scores.findIndex(score => score.type === currentScoreType.value);
        if (scoreIndex >= 0) {
            (formData.scores[scoreIndex] as any)[key] = value;
        } else {
            // 创建新的评分记录
            const newScore: EvaluationScore = {
                type: currentScoreType.value,
                performanceScore: undefined,
                attitudeScore: undefined,
                abilityScore: undefined,
                growthScore: undefined
            };
            (newScore as any)[key] = value;
            formData.scores.push(newScore);
        }
    };

    const evaluationTags = computed(() => {
        const tags = [];

        // 根据用户角色显示对应的评价类型标签
        if (props.userRole?.type === 'employee') {
            tags.push({
                type: 'self' as EvaluationStatus,
                label: '员工自评',
                active: formData.evaluationTypes.includes('self'),
                available: true
            });
        }

        if (props.userRole?.type === 'colleague') {
            tags.push({
                type: 'colleague' as EvaluationStatus,
                label: '同事评价',
                active: formData.evaluationTypes.includes('colleague'),
                available: true
            });
        }

        if (props.userRole?.type === 'manager') {
            tags.push({
                type: 'manager' as EvaluationStatus,
                label: '主管审核',
                active: formData.evaluationTypes.includes('manager'),
                available: true
            });
        }

        if (props.userRole?.type === 'admin') {
            // 管理员可以看到所有类型，但通常不直接评价
            tags.push(
                {
                    type: 'self' as EvaluationStatus,
                    label: '员工自评',
                    active: formData.evaluationTypes.includes('self'),
                    available: false // 管理员不能直接操作
                },
                {
                    type: 'colleague' as EvaluationStatus,
                    label: '同事评价',
                    active: formData.evaluationTypes.includes('colleague'),
                    available: false
                },
                {
                    type: 'manager' as EvaluationStatus,
                    label: '主管审核',
                    active: formData.evaluationTypes.includes('manager'),
                    available: false
                }
            );
        }

        return tags;
    });

    const scoreItems = [
        {
            key: 'performanceScore' as keyof EvaluationData['scores'],
            label: '工作业绩',
            min: 0,
            max: 60,
            unit: '分'
        },
        {
            key: 'attitudeScore' as keyof EvaluationData['scores'],
            label: '工作态度',
            min: 0,
            max: 10,
            unit: '分'
        },
        {
            key: 'abilityScore' as keyof EvaluationData['scores'],
            label: '工作能力',
            min: 0,
            max: 10,
            unit: '分'
        },
        {
            key: 'growthScore' as keyof EvaluationData['scores'],
            label: '个人成长',
            min: 0,
            max: 10,
            unit: '分'
        }
    ];

    // 计算最大总分（评分项目总分，不包含线上转发）
    const maxTotalScore = computed(() => {
        return scoreItems.reduce((sum, item) => sum + item.max, 0);
    });

    // 计算当前评分的小计
    const subtotal = computed(() => {
        const score = currentScore.value;
        return (score.performanceScore || 0) +
               (score.attitudeScore || 0) +
               (score.abilityScore || 0) +
               (score.growthScore || 0);
    });

    // 计算平均分（基于当前评分项目，不包含线上转发）
    const averageScore = computed(() => {
        const total = subtotal.value || 0;
        const max = maxTotalScore.value;
        if (max === 0) return 0;
        return (total / max) * 100; // 转换为百分比
    });

    // 计算最终得分
    const finalScore = computed(() => {
        const employeeScore = formData.scores.find(s => s.type === 'employee');
        const colleagueScore = formData.scores.find(s => s.type === 'colleague');
        const managerScore = formData.scores.find(s => s.type === 'manager');

        const employeeTotal = employeeScore ?
            (employeeScore.performanceScore || 0) +
            (employeeScore.attitudeScore || 0) +
            (employeeScore.abilityScore || 0) +
            (employeeScore.growthScore || 0) : 0;

        const colleagueTotal = colleagueScore ?
            (colleagueScore.performanceScore || 0) +
            (colleagueScore.attitudeScore || 0) +
            (colleagueScore.abilityScore || 0) +
            (colleagueScore.growthScore || 0) : 0;

        const managerTotal = managerScore ?
            (managerScore.performanceScore || 0) +
            (managerScore.attitudeScore || 0) +
            (managerScore.abilityScore || 0) +
            (managerScore.growthScore || 0) : 0;

        const additionalScore = formData.additionalScore || 0;

        // 如果没有同事评分，使用不同的计算方式
        if (colleagueTotal === 0) {
            return employeeTotal * 0.1 + managerTotal * 0.9 + additionalScore;
        } else {
            return employeeTotal * 0.1 + colleagueTotal * 0.2 + managerTotal * 0.7 + additionalScore;
        }
    });

    // 方法
    const toggleTag = (type: EvaluationStatus, checked: boolean) => {
        if (!canEdit.value) return;

        // 检查该标签是否可用
        const tag = evaluationTags.value.find(t => t.type === type);
        if (!tag?.available) return;

        if (checked) {
            if (!formData.evaluationTypes.includes(type)) {
                formData.evaluationTypes.push(type);
            }
        } else {
            const index = formData.evaluationTypes.indexOf(type);
            if (index > -1) {
                formData.evaluationTypes.splice(index, 1);
            }
        }
    };



    const getGradeType = (score: number): 'success' | 'warning' | 'error' | 'info' => {
        if (score >= 90) return 'success';
        if (score >= 80) return 'info';
        if (score >= 70) return 'warning';
        return 'error';
    };

    const getGradeText = (score: number): string => {
        if (score >= 90) return '优秀';
        if (score >= 80) return '良好';
        if (score >= 70) return '合格';
        if (score >= 60) return '待改进';
        return '不合格';
    };

    const handleSave = () => {
        // 验证必填项
        if (!formData.department || !formData.name || !formData.reviewDate) {
            message.error('请填写完整的基本信息');
            return;
        }

        if (formData.evaluationTypes.length === 0) {
            message.error('请至少选择一种评价类型');
            return;
        }

        const hasValidScore = formData.scores.some(score =>
            (score.performanceScore || 0) +
            (score.attitudeScore || 0) +
            (score.abilityScore || 0) +
            (score.growthScore || 0) > 0
        );
        if (!hasValidScore) {
            message.error('请至少填写一项评分');
            return;
        }

        const dataToSave: EvaluationData = {
            ...formData,
            finalScore: finalScore.value,
            updatedBy: 'current_user', // 这里应该从用户上下文获取
            createdAt: formData.createdAt || Date.now()
        };

        emit('save', dataToSave);
        message.success('保存成功');
    };

    const handleReset = () => {
        Object.assign(formData, {
            department: '',
            name: '',
            reviewDate: null,
            evaluationTypes: [],
            scores: [],
            comment: '',
            additionalScore: null
        });
        emit('reset');
        message.info('已重置表单');
    };

    const handlePreview = () => {
        emit('preview', { ...formData });
    };

    // 格式化日期函数
    const formatDate = (timestamp: number | null | undefined): string => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    };

    // 监听用户角色变化，自动设置评价类型
    watch(() => props.userRole?.type, (newRole) => {
        if (newRole && formData.evaluationTypes.length === 0) {
            // 根据用户角色自动设置对应的评价类型
            switch (newRole) {
                case 'employee':
                    formData.evaluationTypes = ['self'];
                    break;
                case 'colleague':
                    formData.evaluationTypes = ['colleague'];
                    break;
                case 'manager':
                    formData.evaluationTypes = ['manager'];
                    break;
                case 'admin':
                    // 管理员默认不设置评价类型
                    break;
            }
        }
    }, { immediate: true });

    // 监听 props 变化
    watch(() => props.modelValue, (newValue) => {
        Object.assign(formData, newValue);
    }, { deep: true });

    // 监听表单数据变化，向上传递
    watch(formData, (newValue) => {
        emit('update:modelValue', { ...newValue });
    }, { deep: true });
</script>

<style scoped>
    .evaluation-card {
        max-width: 100%;
        margin: 0 auto;
    }

    .basic-info-section {
        margin-bottom: 20px;
    }

    .basic-form {
        width: 100%;
    }

    /* 紧凑基础信息样式 */
    .basic-info-compact {
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .compact-info-row {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
        font-size: 14px;
    }

    .compact-info-item {
        display: flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
    }

    .compact-label {
        font-weight: 500;
        color: #666;
        font-size: 13px;
    }

    .compact-separator {
        color: #ccc;
        margin: 0 4px;
        font-weight: 300;
    }

    /* 区域标题样式 */
    .section-title {
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
    }

    /* 评价类型区域 */
    .evaluation-type-section {
        margin: 20px 0;
    }

    .evaluation-tag {
        margin: 4px;
        transition: all 0.3s ease;
    }

    .evaluation-tag:hover:not(.tag-disabled) {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .tag-disabled {
        cursor: not-allowed !important;
        opacity: 0.7;
    }

    .tag-disabled:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* 评分区域 */
    .scoring-section {
        margin: 20px 0;
    }

    .score-list {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .score-item-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background-color: #fafafa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .score-item-row:hover {
        background-color: #f5f5f5;
        border-color: #2080f0;
        box-shadow: 0 2px 8px rgba(32, 128, 240, 0.1);
    }

    .score-label-section {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .score-input-section {
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }

    .score-input {
        text-align: center;
    }

    /* 小计区域 - 紧凑设计 */
    .subtotal-compact {
        margin: 16px 0;
        padding: 12px 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .subtotal-row {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
    }

    .subtotal-label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
    }

    .subtotal-extra {
        color: #888;
        font-size: 12px;
        margin-left: 8px;
    }

    /* 最终得分区域 */
    .final-score-section {
        margin: 20px 0;
    }

    .final-score-content {
        padding: 16px 20px;
        background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
        border: 2px solid #f0a020;
        border-radius: 8px;
        text-align: center;
    }

    .final-score-title {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
    }

    .final-score-value {
        font-size: 24px;
        font-weight: bold;
    }

    /* 评分类型切换 */
    .score-type-tabs {
        margin: 16px 0 12px 0;
        padding: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    /* 线上转发区域 */
    .additional-score-section {
        margin: 20px 0;
    }

    .additional-score-edit {
        margin-top: 16px;
    }

    .additional-score-input {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .additional-score-readonly {
        margin-top: 16px;
    }

    .readonly-score-item {
        padding: 12px 16px;
        background-color: #fafafa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .readonly-label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
    }

    .readonly-value {
        display: flex;
        align-items: center;
    }

    /* 备注区域 */
    .remarks-section {
        margin: 20px 0;
    }

    .remarks-input {
        margin-top: 8px;
    }

    /* 操作按钮区域 */
    .action-section {
        margin-top: 32px;
        padding-top: 20px;
        border-top: 2px solid #f0f0f0;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .evaluation-card {
            margin: 0;
            border-radius: 0;
        }

        .compact-info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px 16px;
            align-items: center;
        }

        .compact-info-item:nth-child(1) {
            grid-column: 1;
            grid-row: 1;
        }

        .compact-info-item:nth-child(3) {
            grid-column: 2;
            grid-row: 1;
        }

        .compact-info-item:nth-child(5) {
            grid-column: 1 / -1;
            grid-row: 2;
            justify-self: center;
        }

        .compact-separator {
            display: none;
        }

        .score-item-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
            padding: 12px;
        }

        .score-input-section {
            width: 100%;
        }

        .subtotal-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }

        .subtotal-extra {
            margin-left: 0;
        }

        .additional-score-input {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }

        .readonly-score-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .section-title {
            text-align: center;
        }

        .evaluation-tag {
            margin: 2px;
        }
    }

    /* 小屏幕优化 */
    @media (max-width: 480px) {
        .basic-info-compact {
            padding: 10px 12px;
        }

        .compact-info-row {
            font-size: 13px;
        }

        .score-item-row {
            padding: 10px 12px;
        }

        .subtotal-compact {
            padding: 10px 12px;
        }

        .action-section {
            margin-top: 24px;
            padding-top: 16px;
        }
    }

    /* 深度样式 */
    :deep(.n-form-item-label) {
        font-weight: 500;
    }

    :deep(.n-input-number .n-input__input-el) {
        text-align: center;
    }

    :deep(.n-date-picker) {
        width: 100%;
    }

    :deep(.n-card-header) {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
    }

    /* 标签动画 */
    :deep(.n-tag) {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    :deep(.n-tag:hover) {
        transform: translateY(-1px);
    }

    /* 按钮样式 */
    :deep(.n-button) {
        transition: all 0.3s ease;
    }

    :deep(.n-button:hover) {
        transform: translateY(-1px);
    }
</style>