<template>
  <n-card :title="cardTitle" size="small" :bordered="true" class="evaluation-card">
    <!-- 基本信息区域 -->
    <div class="basic-info-section">
      <!-- 员工角色：可编辑表单 -->
      <n-form
        v-if="props.userRole?.type === 'employee'"
        :model="formData"
        :label-placement="isMobile ? 'top' : 'left'"
        :label-width="isMobile ? 'auto' : '80px'"
        class="basic-form"
      >
        <n-grid :cols="isMobile ? 1 : 3" :x-gap="16" :y-gap="16">
          <n-form-item-gi label="部门">
            <n-input
              v-model:value="formData.department"
              placeholder="请输入部门"
            />
          </n-form-item-gi>
          <n-form-item-gi label="姓名">
            <n-input
              v-model:value="formData.name"
              placeholder="请输入姓名"
            />
          </n-form-item-gi>
          <n-form-item-gi label="评价日期">
            <n-date-picker
              v-model:value="formData.reviewDate"
              type="date"
              placeholder="选择评价日期"
              style="width: 100%"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>

      <!-- 其他角色：只读显示 -->
      <div v-else class="basic-info-readonly">
        <div class="readonly-grid">
          <div class="info-item">
            <div class="info-label">部门</div>
            <div class="info-value">
              <n-text>{{ formData.department || '未填写' }}</n-text>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">姓名</div>
            <div class="info-value">
              <n-text strong>{{ formData.name || '未填写' }}</n-text>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">评价日期</div>
            <div class="info-value">
              <n-text>{{ formatDate(formData.reviewDate) || '未选择' }}</n-text>
            </div>
          </div>
        </div>
      </div>
    </div>

    <n-divider />

    <!-- 评价类型区域 -->
    <div class="evaluation-type-section">
      <div class="section-title">
        <n-text strong>评价类型</n-text>
      </div>
      <n-space :wrap="true" :size="[8, 8]">
        <n-tag
          v-for="tag in evaluationTags"
          :key="tag.type"
          :type="tag.active ? 'primary' : (tag.available ? 'default' : 'info')"
          :bordered="false"
          :checkable="tag.available"
          :checked="tag.active"
          @update:checked="(checked) => toggleTag(tag.type, checked)"
          :size="isMobile ? 'medium' : 'large'"
          :class="['evaluation-tag', { 'tag-disabled': !tag.available }]"
        >
          {{ tag.label }}
          <span v-if="!tag.available" style="margin-left: 4px; opacity: 0.6;">
            (仅查看)
          </span>
        </n-tag>
      </n-space>
    </div>

    <n-divider />

    <!-- 评分项目区域 -->
    <div class="scoring-section">
      <div class="section-title">
        <n-text strong>评分项目</n-text>
        <n-text depth="3" style="font-size: 12px; margin-left: 8px">
          (满分100分)
        </n-text>
      </div>

      <div class="score-grid">
        <div class="score-item" v-for="item in scoreItems" :key="item.key">
          <div class="score-item-header">
            <n-text strong>{{ item.label }}</n-text>
          </div>
          <div class="score-item-content">
            <div class="score-input-wrapper">
              <n-input-number
                v-model:value="formData.scores[item.key]"
                :min="0"
                :max="100"
                :step="1"
                :precision="1"
                placeholder="请输入分数"
                :disabled="!canEdit"
                :style="{ width: isMobile ? '100%' : '120px' }"
                class="score-input"
              />
              <span class="score-unit">分</span>
            </div>
            <div class="score-description">
              <n-text depth="3" style="font-size: 12px">
                {{ getScoreDescription(formData.scores[item.key]) }}
              </n-text>
            </div>
          </div>
        </div>
      </div>

      <!-- 小计区域 -->
      <n-divider />
      <div class="subtotal-section">
        <div class="subtotal-header">
          <n-text strong style="font-size: 16px">评分汇总</n-text>
        </div>
        <div class="subtotal-content">
          <div class="subtotal-item">
            <span class="subtotal-label">总分：</span>
            <n-text strong style="font-size: 18px; color: #2080f0">
              {{ (subtotal || 0).toFixed(1) }} 分
            </n-text>
          </div>
          <div class="subtotal-item">
            <span class="subtotal-label">平均分：</span>
            <n-text style="font-size: 16px">
              {{ averageScore.toFixed(1) }} 分
            </n-text>
          </div>
          <div class="subtotal-item">
            <span class="subtotal-label">等级：</span>
            <n-tag
              :type="getGradeType(averageScore)"
              size="large"
              class="grade-tag"
            >
              {{ getGradeText(averageScore) }}
            </n-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 备注区域 -->
    <n-divider />
    <div class="remarks-section">
      <div class="section-title">
        <n-text strong>备注说明</n-text>
      </div>
      <n-input
        v-model:value="formData.comment"
        type="textarea"
        placeholder="请输入备注信息..."
        :rows="isMobile ? 4 : 3"
        :disabled="!canEdit"
        class="remarks-input"
      />
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section" v-if="showActions">
      <n-space
        :justify="isMobile ? 'space-around' : 'center'"
        :size="isMobile ? 'small' : 'medium'"
        :wrap="isMobile"
      >
        <n-button
          @click="handleReset"
          :disabled="!canEdit"
          :style="{ width: isMobile ? '100px' : 'auto' }"
        >
          重置
        </n-button>
        <n-button
          type="primary"
          @click="handleSave"
          :disabled="!canEdit"
          :style="{ width: isMobile ? '100px' : 'auto' }"
        >
          保存
        </n-button>
        <n-button
          type="info"
          @click="handlePreview"
          :style="{ width: isMobile ? '100px' : 'auto' }"
        >
          预览
        </n-button>
      </n-space>
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { computed, reactive, watch, onMounted, onUnmounted, ref } from 'vue';
import {
  NCard,
  NForm,
  NFormItemGi,
  NGrid,
  NInput,
  NInputNumber,
  NDatePicker,
  NTag,
  NSpace,
  NDivider,
  NText,
  NButton,
  useMessage
} from 'naive-ui';
import type { UserRole, EvaluationStatus } from '../../../fconfig/fyfc/review';

// 类型定义 - 根据配置文件更新
export interface EvaluationData {
  id?: number;
  department?: string;
  name?: string;
  reviewDate?: number | null;
  evaluationTypes: EvaluationStatus[];
  scores: {
    performanceScore: number | null;    // 绩效得分
    attitudeScore: number | null;       // 态度得分
    abilityScore: number | null;        // 能力得分
    growthScore: number | null;         // 个人成长得分
  };
  score?: number;                       // 小计
  comment?: string;                     // 说明
  additionalScore?: number;             // 线上转发
  status?: string;
  createdAt?: number;
  createdBy?: string;
  updatedBy?: string;
}

export interface UserRoleConfig {
  type: UserRole;
  canEdit: boolean;
  canView: boolean;
}

// Props 定义
interface Props {
  modelValue?: EvaluationData;
  userRole?: UserRoleConfig;
  title?: string;
  showActions?: boolean;
}

// Emits 定义
interface Emits {
  (e: 'update:modelValue', value: EvaluationData): void;
  (e: 'save', value: EvaluationData): void;
  (e: 'reset'): void;
  (e: 'preview', value: EvaluationData): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    department: '',
    name: '',
    reviewDate: null,
    evaluationTypes: [],
    scores: {
      performanceScore: null,
      attitudeScore: null,
      abilityScore: null,
      growthScore: null
    },
    comment: ''
  }),
  userRole: () => ({
    type: 'employee',
    canEdit: true,
    canView: true
  }),
  title: '方远房地产集团员工月度绩效考核评分表',
  showActions: true
});

const emit = defineEmits<Emits>();
const message = useMessage();

// 移动端检测
const isMobile = ref(false);

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// 响应式数据
const formData = reactive<EvaluationData>({ ...props.modelValue });

// 计算属性
const cardTitle = computed(() => props.title);

const canEdit = computed(() => props.userRole?.canEdit ?? true);

const evaluationTags = computed(() => {
  const tags = [];

  // 根据用户角色显示对应的评价类型标签
  if (props.userRole?.type === 'employee') {
    tags.push({
      type: 'self' as EvaluationStatus,
      label: '员工自评',
      active: formData.evaluationTypes.includes('self'),
      available: true
    });
  }

  if (props.userRole?.type === 'colleague') {
    tags.push({
      type: 'colleague' as EvaluationStatus,
      label: '同事评价',
      active: formData.evaluationTypes.includes('colleague'),
      available: true
    });
  }

  if (props.userRole?.type === 'manager') {
    tags.push({
      type: 'manager' as EvaluationStatus,
      label: '主管审核',
      active: formData.evaluationTypes.includes('manager'),
      available: true
    });
  }

  if (props.userRole?.type === 'admin') {
    // 管理员可以看到所有类型，但通常不直接评价
    tags.push(
      {
        type: 'self' as EvaluationStatus,
        label: '员工自评',
        active: formData.evaluationTypes.includes('self'),
        available: false // 管理员不能直接操作
      },
      {
        type: 'colleague' as EvaluationStatus,
        label: '同事评价',
        active: formData.evaluationTypes.includes('colleague'),
        available: false
      },
      {
        type: 'manager' as EvaluationStatus,
        label: '主管审核',
        active: formData.evaluationTypes.includes('manager'),
        available: false
      }
    );
  }

  return tags;
});

const scoreItems = [
  { key: 'performanceScore' as keyof EvaluationData['scores'], label: '工作业绩' },
  { key: 'attitudeScore' as keyof EvaluationData['scores'], label: '工作态度' },
  { key: 'abilityScore' as keyof EvaluationData['scores'], label: '工作能力' },
  { key: 'growthScore' as keyof EvaluationData['scores'], label: '个人成长' }
];

// 计算小计和平均分
const subtotal = computed(() => {
  const scores = Object.values(formData.scores);
  return scores.reduce((sum, score) => (sum || 0) + (score || 0), 0);
});

const averageScore = computed(() => {
  const scores = Object.values(formData.scores).filter((score): score is number => score !== null && score > 0);
  if (scores.length === 0) return 0;
  return scores.reduce((sum, score) => sum + score, 0) / scores.length;
});

// 方法
const toggleTag = (type: EvaluationStatus, checked: boolean) => {
  if (!canEdit.value) return;

  // 检查该标签是否可用
  const tag = evaluationTags.value.find(t => t.type === type);
  if (!tag?.available) return;

  if (checked) {
    if (!formData.evaluationTypes.includes(type)) {
      formData.evaluationTypes.push(type);
    }
  } else {
    const index = formData.evaluationTypes.indexOf(type);
    if (index > -1) {
      formData.evaluationTypes.splice(index, 1);
    }
  }
};

const getScoreDescription = (score: number | null): string => {
  if (score === null) return '';
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '合格';
  if (score >= 60) return '待改进';
  return '不合格';
};

const getGradeType = (score: number): 'success' | 'warning' | 'error' | 'info' => {
  if (score >= 90) return 'success';
  if (score >= 80) return 'info';
  if (score >= 70) return 'warning';
  return 'error';
};

const getGradeText = (score: number): string => {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '合格';
  if (score >= 60) return '待改进';
  return '不合格';
};

const handleSave = () => {
  // 验证必填项
  if (!formData.department || !formData.name || !formData.reviewDate) {
    message.error('请填写完整的基本信息');
    return;
  }

  if (formData.evaluationTypes.length === 0) {
    message.error('请至少选择一种评价类型');
    return;
  }

  const hasValidScore = Object.values(formData.scores).some(score => score !== null && score > 0);
  if (!hasValidScore) {
    message.error('请至少填写一项评分');
    return;
  }

  // 计算小计
  const calculatedScore = Object.values(formData.scores).reduce((sum, score) => (sum || 0) + (score || 0), 0);
  const dataToSave: EvaluationData = {
    ...formData,
    score: calculatedScore || undefined,
    updatedBy: 'current_user', // 这里应该从用户上下文获取
    createdAt: formData.createdAt || Date.now()
  };

  emit('save', dataToSave);
  message.success('保存成功');
};

const handleReset = () => {
  Object.assign(formData, {
    department: '',
    name: '',
    reviewDate: null,
    evaluationTypes: [],
    scores: {
      performanceScore: null,
      attitudeScore: null,
      abilityScore: null,
      growthScore: null
    },
    comment: ''
  });
  emit('reset');
  message.info('已重置表单');
};

const handlePreview = () => {
  emit('preview', { ...formData });
};

// 格式化日期函数
const formatDate = (timestamp: number | null | undefined): string => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 监听用户角色变化，自动设置评价类型
watch(() => props.userRole?.type, (newRole) => {
  if (newRole && formData.evaluationTypes.length === 0) {
    // 根据用户角色自动设置对应的评价类型
    switch (newRole) {
      case 'employee':
        formData.evaluationTypes = ['self'];
        break;
      case 'colleague':
        formData.evaluationTypes = ['colleague'];
        break;
      case 'manager':
        formData.evaluationTypes = ['manager'];
        break;
      case 'admin':
        // 管理员默认不设置评价类型
        break;
    }
  }
}, { immediate: true });

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue);
}, { deep: true });

// 监听表单数据变化，向上传递
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });
</script>

<style scoped>
.evaluation-card {
  max-width: 100%;
  margin: 0 auto;
}

.basic-info-section {
  margin-bottom: 20px;
}

.basic-form {
  width: 100%;
}

/* 只读信息显示样式 */
.basic-info-readonly {
  width: 100%;
}

.readonly-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.info-item:hover {
  background-color: #f5f5f5;
  border-color: #d6d9dc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  color: #333;
  min-height: 24px;
  display: flex;
  align-items: center;
  font-weight: 500;
}

/* 区域标题样式 */
.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

/* 评价类型区域 */
.evaluation-type-section {
  margin: 20px 0;
}

.evaluation-tag {
  margin: 4px;
  transition: all 0.3s ease;
}

.evaluation-tag:hover:not(.tag-disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-disabled {
  cursor: not-allowed !important;
  opacity: 0.7;
}

.tag-disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 评分区域 */
.scoring-section {
  margin: 20px 0;
}

.score-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin: 16px 0;
}

.score-item {
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.score-item:hover {
  border-color: #2080f0;
  box-shadow: 0 2px 8px rgba(32, 128, 240, 0.1);
}

.score-item-header {
  margin-bottom: 12px;
  text-align: center;
}

.score-item-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.score-input-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.score-input {
  text-align: center;
}

.score-unit {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.score-description {
  text-align: center;
  min-height: 20px;
}

/* 小计区域 */
.subtotal-section {
  margin: 20px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.subtotal-header {
  margin-bottom: 16px;
  text-align: center;
}

.subtotal-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.subtotal-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtotal-label {
  font-weight: 500;
  color: #666;
}

.grade-tag {
  font-weight: bold;
}

/* 备注区域 */
.remarks-section {
  margin: 20px 0;
}

.remarks-input {
  margin-top: 8px;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 2px solid #f0f0f0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .evaluation-card {
    margin: 0;
    border-radius: 0;
  }

  .readonly-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-item {
    padding: 12px;
    min-height: 60px;
  }

  .info-label {
    font-size: 11px;
    margin-bottom: 4px;
  }

  .info-value {
    font-size: 14px;
  }

  .score-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .score-item {
    padding: 12px;
  }

  .score-input-wrapper {
    flex-direction: column;
    gap: 4px;
  }

  .subtotal-content {
    flex-direction: column;
    gap: 12px;
  }

  .subtotal-item {
    width: 100%;
    justify-content: center;
  }

  .section-title {
    text-align: center;
  }

  .evaluation-tag {
    margin: 2px;
  }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
  .basic-info-section {
    margin-bottom: 16px;
  }

  .info-item {
    padding: 10px;
    min-height: 50px;
  }

  .info-label {
    font-size: 10px;
    margin-bottom: 2px;
  }

  .info-value {
    font-size: 13px;
  }

  .score-item {
    padding: 8px;
  }

  .subtotal-section {
    padding: 16px;
  }

  .action-section {
    margin-top: 24px;
    padding-top: 16px;
  }
}

/* 深度样式 */
:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-input-number .n-input__input-el) {
  text-align: center;
}

:deep(.n-date-picker) {
  width: 100%;
}

:deep(.n-card-header) {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
}

/* 标签动画 */
:deep(.n-tag) {
  cursor: pointer;
  transition: all 0.3s ease;
}

:deep(.n-tag:hover) {
  transform: translateY(-1px);
}

/* 按钮样式 */
:deep(.n-button) {
  transition: all 0.3s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}
</style>