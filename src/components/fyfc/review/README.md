# EvaluationEditor 组件使用说明

## 概述

`EvaluationEditor` 是一个可复用的员工绩效考核评分表组件，基于 `src/fconfig/fyfc/review/index.ts` 配置文件设计，支持多种评价类型和完整的评分功能，具有优秀的移动端适配。

## 功能特性

### ✅ 基本信息管理
- 部门输入
- 姓名输入
- 评价日期选择

### ✅ 评价类型标签
- 员工自评 (self)
- 同事评价 (colleague)
- 主管审核 (manager)
- 支持多选和动态切换

### ✅ 评分项目
- 工作业绩 (performanceScore: 0-100分)
- 工作态度 (attitudeScore: 0-100分)
- 工作能力 (abilityScore: 0-100分)
- 个人成长 (growthScore: 0-100分)

### ✅ 自动计算
- 小计：所有评分项的总和
- 平均分：有效评分的平均值
- 等级评定：优秀/良好/合格/待改进/不合格

### ✅ 权限控制
- 基于用户角色的编辑权限
- 只读模式支持

### ✅ 移动端优化
- 响应式布局设计
- 移动端友好的表单排版
- 触摸优化的交互体验

### ✅ 操作功能
- 保存：验证并保存数据
- 重置：清空表单
- 预览：查看数据

## 使用方法

### 基本用法

```vue
<template>
  <EvaluationEditor
    v-model="evaluationData"
    :user-role="userRole"
    @save="handleSave"
    @reset="handleReset"
    @preview="handlePreview"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import EvaluationEditor, {
  type EvaluationData,
  type UserRoleConfig
} from '@/components/fyfc/review/EvaluationEditor.vue';

// 评价数据
const evaluationData = ref<EvaluationData>({
  department: '技术部',
  name: '张三',
  reviewDate: Date.now(),
  evaluationTypes: ['self'],
  scores: {
    performanceScore: 85,
    attitudeScore: 90,
    abilityScore: 88,
    growthScore: 82
  },
  comment: '表现良好，继续保持。'
});

// 用户角色
const userRole = ref<UserRoleConfig>({
  type: 'employee',
  canEdit: true,
  canView: true
});

// 事件处理
const handleSave = (data: EvaluationData) => {
  console.log('保存数据:', data);
};

const handleReset = () => {
  console.log('重置表单');
};

const handlePreview = (data: EvaluationData) => {
  console.log('预览数据:', data);
};
</script>
```

### 只读模式

```vue
<EvaluationEditor
  v-model="evaluationData"
  :user-role="{ type: 'admin', canEdit: false, canView: true }"
  :show-actions="false"
/>
```

### 自定义标题

```vue
<EvaluationEditor
  v-model="evaluationData"
  :user-role="userRole"
  title="员工季度绩效考核评分表"
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `EvaluationData` | 默认空对象 | 评价数据 |
| `userRole` | `UserRoleConfig` | `{ type: 'employee', canEdit: true, canView: true }` | 用户角色配置 |
| `title` | `string` | `'方远房地产集团员工月度绩效考核评分表'` | 卡片标题 |
| `showActions` | `boolean` | `true` | 是否显示操作按钮 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `EvaluationData` | 数据更新时触发 |
| `save` | `EvaluationData` | 点击保存按钮时触发 |
| `reset` | - | 点击重置按钮时触发 |
| `preview` | `EvaluationData` | 点击预览按钮时触发 |

## 类型定义

### EvaluationData

```typescript
interface EvaluationData {
  id?: number;                          // 评价ID
  department?: string;                  // 部门
  name?: string;                       // 姓名
  reviewDate?: number | null;          // 评价日期时间戳
  evaluationTypes: EvaluationStatus[]; // 评价类型数组
  scores: {
    performanceScore: number | null;   // 绩效得分
    attitudeScore: number | null;      // 态度得分
    abilityScore: number | null;       // 能力得分
    growthScore: number | null;        // 个人成长得分
  };
  score?: number;                      // 小计
  comment?: string;                    // 说明备注
  additionalScore?: number;            // 线上转发
  status?: string;                     // 状态
  createdAt?: number;                  // 创建时间
  createdBy?: string;                  // 创建人
  updatedBy?: string;                  // 更新人
}
```

### UserRoleConfig

```typescript
interface UserRoleConfig {
  type: UserRole;      // 用户角色类型
  canEdit: boolean;    // 是否可编辑
  canView: boolean;    // 是否可查看
}

// UserRole 来自配置文件
type UserRole = 'unknown' | 'employee' | 'colleague' | 'manager' | 'admin';

// EvaluationStatus 来自配置文件
type EvaluationStatus = 'self' | 'colleague' | 'manager' | 'completed';
```

## 评分等级

| 分数范围 | 等级 | 标签颜色 |
|----------|------|----------|
| 90-100 | 优秀 | success (绿色) |
| 80-89 | 良好 | info (蓝色) |
| 70-79 | 合格 | warning (橙色) |
| 60-69 | 待改进 | warning (橙色) |
| 0-59 | 不合格 | error (红色) |

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以使用深度选择器：

```vue
<style>
:deep(.evaluation-card) {
  /* 自定义卡片样式 */
}

:deep(.subtotal-container) {
  /* 自定义小计容器样式 */
}
</style>
```

## 响应式设计

组件内置完善的响应式设计，针对不同屏幕尺寸优化：

### 桌面端 (>768px)
- 基本信息采用三列网格布局
- 评分项目采用网格卡片布局
- 标签和按钮使用较大尺寸

### 移动端 (≤768px)
- 基本信息改为单列布局，标签置顶
- 评分项目改为单列卡片布局
- 评分输入框占满宽度
- 小计信息垂直排列
- 按钮适配移动端尺寸

### 小屏幕 (≤480px)
- 进一步优化间距和内边距
- 按钮组自适应排列
- 文本输入框增加行数

### 移动端特性
- 自动检测屏幕尺寸
- 触摸友好的交互元素
- 优化的滚动体验
- 防止意外缩放

## 验证规则

保存时会进行以下验证：

1. ✅ 基本信息完整性（部门、姓名、日期）
2. ✅ 至少选择一种评价类型
3. ✅ 至少填写一项评分

## 注意事项

1. 日期使用时间戳格式存储
2. 评分范围为 0-100 分，支持一位小数
3. 组件依赖 Naive UI，确保项目中已安装
4. 使用 `v-model` 进行双向数据绑定
5. 所有事件都会传递完整的数据副本

## 示例项目

参考 `src/views/fyfc/review/staff/edit/index.vue` 查看完整的使用示例。
