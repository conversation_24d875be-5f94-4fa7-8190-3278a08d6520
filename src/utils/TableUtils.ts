import { DataTableBaseColumn } from "naive-ui";
import { h, reactive } from "vue";
import { NIcon } from "naive-ui";
import { formatDate } from "./xUtils";
import { Search as IconSearch, } from '@vicons/ionicons5'
import MyFilter from '../components/MyFilterComponent.vue';
import MyDatePicker from '../components/MyDateFilterComponent.vue'

export type SETTING = {
    key: string,
    title: string,
    isActivated: true | false
};



export interface ExtendedDataTableBaseColumn extends DataTableBaseColumn {
    isActivated: boolean;
}

export const createColumnWithTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
    const { key } = col as ExtendedDataTableBaseColumn;
    let column = reactive({
        ...col,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }: { hide: any }) => {
            return type === 'text' ? h(MyFilter, {
                hide,
                type: type,
                filterOptionValue: column.filterOptionValue,
                onFilter: (val: any) => {
                    column.filterOptionValue = val;

                    if (doFilter) {
                        doFilter({ key: key, value: val });
                    }
                }
            }) : undefined;
        }
    });
    return column;
}

export const createColumnWithDatePickerTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
    const { key } = col as ExtendedDataTableBaseColumn;
    let column = reactive({
        ...col,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }: { hide: any }) => {
            return type === 'datePicker' ? h(MyDatePicker, {
                hide,
                type: type,
                filterOptionValue: column.filterOptionValue,
                onFilter: (val: any) => {
                    column.filterOptionValue = val;
                    if (doFilter) {
                        doFilter({ key: key + 'JsonStr', value: val });
                    }
                }
            }) : undefined;
        },
        render: (rowData: any) => {
            return formatDate(rowData[key], 'yyyy年M月D日');
        },
    });
    return column;
}

export const createColumnWithFilterOptions = (col: ExtendedDataTableBaseColumn, optionKey: string, optionsMap: any) => {
    let column = reactive({
        ...col,
        filter: true,
        filterMultiple: true,
        filterOptionValues: null,
        filterOptions: optionsMap[optionKey]
    });
    return column;
}