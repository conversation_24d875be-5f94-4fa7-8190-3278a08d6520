import axios, { AxiosRequestConfig, AxiosError, AxiosResponse } from 'axios'
import { stringify } from 'qs'
import VueCookies from 'vue-cookies'

// 设置请求拦截器
axios.interceptors.request.use(
    (config: any) => {
        // 每次发送请求之前判断vue-cookies中是否存在token
        // 如果存在，则统一在http请求的header都加上token，这样后台根据token判断你的登录情况
        // 即使本地存在token，也有可能token是过期的，所以在响应拦截器中要对返回状态进行判断
        const token = VueCookies.get('x-token');
        if (token) {
            config.headers = {
                ...config.headers,
                Authorization: `Bearer ${token}`
            };
        }
        return config;
    },
    (error: AxiosError) => {
        return Promise.reject(error);
    }
);

interface Params {
    [key: string]: any;
}

async function doGetAsync<T>(url: string, token?: string, params?: Params): Promise<T> {
    try {
        const config: AxiosRequestConfig = {
            withCredentials: true, // 确保凭据被包含在请求中
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'token': token,
            },
            params, // `params` are included in the URL request
            paramsSerializer: (params) => stringify(params),
        };

        const response: AxiosResponse<T> = await axios.get<T>(url, config);
        return response.data;
    } catch (error: any) {
        console.error('Request failed:', error.response || error);
        throw error.response || error; // Ensures the error is propagated, considering callers might depend on catching errors.
    }
}

async function doPostWithTokenAsync<T>(url: string, params: Params, token: string): Promise<T> {
    try {
        const config: AxiosRequestConfig = {
            withCredentials: true, // 确保凭据被包含在请求中
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'token': token,
            }
        };
        const response: AxiosResponse<T> = await axios.post<T>(url, stringify(params), config);
        return response.data;
    } catch (error: any) {
        console.error('Request failed:', error.response || error);
        throw error.response || error; // 确保错误被传播
    }
}

async function doPostAsync<T>(url: string, params: Params): Promise<T> {
    try {
        const config: AxiosRequestConfig = {
            withCredentials: true, // 确保凭据被包含在请求中
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        };
        const response: AxiosResponse<T> = await axios.post<T>(url, stringify(params), config);
        return response.data;
    } catch (error: any) {
        console.error('Request failed:', error.response || error);
        throw error.response || error; // 确保错误被传播
    }
}

async function doPutAsync<T>(url: string, params: Params): Promise<T> {
    try {
        const config: AxiosRequestConfig = {
            withCredentials: true, // 确保凭据被包含在请求中
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        };
        const response: AxiosResponse<T> = await axios.put<T>(url, stringify(params), config);
        return response.data;
    } catch (error: any) {
        console.error('Request failed:', error.response || error);
        throw error.response || error; // 确保错误被传播
    }
}

export { doPostWithTokenAsync, doPostAsync, doPutAsync, doGetAsync };
