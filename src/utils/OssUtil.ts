import { doPostAsync } from "./AxiosUtil";

interface OssDeleteResponse {
    code: number;
    errMsg: any;
}

const ossDelete = (key: any, ossSchema: any): Promise<OssDeleteResponse> => {
    return new Promise((resolve, reject) => {
        const url = '/fyschedule/oss/delete';
        const param = {
            key: key,
            bucket: ossSchema
        };
        doPostAsync(url, param).then((res: any) => {
            resolve({ code: 0, errMsg: res });
        }).catch((err: any) => {
            reject({ code: 1, errMsg: err });
        });
    });
};

const doUpload = (url: any, param: any, ossSchema: any): Promise<any> => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            doPostAsync(url, param).then((res: any) => {
                console.info('upload', res);
                resolve(res);
            }).catch((err: any) => {
                if (err && err.content) {
                    ossDelete(err.content, ossSchema);
                }
                reject(err);
            });
        }, 1000);
    });
};

const deleteItem = (url: any, id: any, key: any, ossSchema: any): Promise<OssDeleteResponse> => {
    return new Promise((resolve, reject) => {
        const param = {
            id: parseInt(id)
        };
        doPostAsync(url, param).then((res: any) => {
            if (res.code === 0) {
                ossDelete(key, ossSchema).then((deleteRes: OssDeleteResponse) => {
                    if (deleteRes.code === 0) {
                        resolve({ code: 0, errMsg: '删除成功' });
                    } else {
                        resolve({ code: 1, errMsg: '删除失败' });
                    }
                });
            } else {
                reject({ code: 1, errMsg: '删除错误' });
            }
        }).catch((err: any) => {
            console.info('err', err);
            reject({ code: 1, errMsg: '删除错误' });
        });
    });
};

export { 
    doUpload, deleteItem, ossDelete
};
