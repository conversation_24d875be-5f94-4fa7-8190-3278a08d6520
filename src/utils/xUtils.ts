import moment from "moment"
const formatDate = (val: any, format: string) => {
    try {
        if (val != null && val != undefined) {
            if (typeof val === 'number') {
                const date = moment(val)
                return date.format(format)
            } else {
                return val;
            } 
        }
        return undefined
    } catch (err) {
        console.log(`output->err`,err)
        return val;
    }
}

function toPercentage(value: number | string | undefined): string | undefined {
    try {
        if (typeof value === 'number') {
            return `${(value * 100).toFixed(0)}%`;
        } else if (typeof value === 'string') {
            return value;
        } else {
            return undefined;
        }
    } catch (err) {
        return undefined;
    }
}
// 定义一个函数，用于将日期字符串转换为时间戳
/**
 * Convert various date formats to a UNIX timestamp.
 * @param date - The date to convert, which can be a string, a Date object, or a number.
 * @returns The UNIX timestamp.
 */
function convertDateToTimestamp(date: string | Date | number): number {
    if (date instanceof Date) {
        // Date对象，获取时间戳
        return date.getTime();
    } else if (typeof date === 'string') {
        // 字符串，转为Date对象获取时间戳
        const parsedDate = Date.parse(date);
        if (!isNaN(parsedDate)) {
            return parsedDate;
        }
        throw new Error(`Invalid date string format: ${date}`);
    } else if (typeof date === 'number') {
        // 已经是时间戳
        return date;
    } else {
        throw new TypeError(`Unsupported date type: ${typeof date}`);
    }
}

function daysBetweenTimestamps(t1: number, t2: number): number {
    // Validate inputs: Ensure that both t1 and t2 are numbers and not null/undefined
    if (typeof t1 !== 'number' || typeof t2 !== 'number') {
        throw new Error('Invalid input: Timestamps must be numbers.');
    }
    if (!isFinite(t1) || !isFinite(t2)) {
        throw new Error('Invalid input: Timestamps must be finite numbers.');
    }

    const millisecondsPerDay = 1000 * 60 * 60 * 24; // 86,400,000 milliseconds in a day
    const diffInMilliseconds = Math.abs(t2 - t1); // Difference in milliseconds
    return Math.floor(diffInMilliseconds / millisecondsPerDay);
}

function normalizeValues(values: string[]): (number | string | null)[] {
    return values.map(value => {
        // 检查是否为数字类型的字符串，如果是，则转换为数字
        if (/^\d+$/.test(value)) {
            return Number(value);
        } else {
            //不是数字的直接返回就好了
            return value;
        }
    }).filter(item => item !== null);
}

export {
    formatDate, toPercentage, convertDateToTimestamp, daysBetweenTimestamps, normalizeValues
}
