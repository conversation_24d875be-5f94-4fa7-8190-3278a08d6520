import { computed, Ref } from 'vue';
import { doGetAsync } from './AxiosUtil';
import { decode } from 'js-base64';

const checkOperatingSystem = (isMobile: Ref<boolean>) => {
    const platform = navigator.userAgent;
    const isiOS = !!platform.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    const isAndroid = !!platform.match(/\(L[^;]+;( U;)? Android./);
    isMobile.value = isiOS || isAndroid;
}

const getPermission = (param: { edit: string, read: string }) => {
    return new Promise((resolve, reject) => {
        let url = '/aliapi/auth/fe/permission/fyapp?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
        doGetAsync(url, undefined).then((res: any) => {
            if (res.code === '00000') {
                let elems = res.data.elem;
                for (let elem of elems) {
                    if (elem['code'] === param.edit) {
                        resolve({ code: 0, data: true, scope: 'edit' });
                        break;
                    } else if (elem['code'] === param.read) {
                        resolve({ code: 0, data: true, scope: 'read' });
                        break;
                    }
                }
                resolve({ code: 0, data: false });
            }
        }).catch((err) => {
            reject(err.response);
        });
    });
}

const isAndroid = (osCheck: Ref<boolean>) => {
    const u = navigator.userAgent;
    const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    osCheck.value = !isiOS;
}

const getOptions = (array: Ref<string[]>, val: string) => {
    return computed(() => {
        let list: string[] = [];
        array.value.forEach(res => {
            if (res.indexOf(val) != -1) {
                list.push(res);
            }
        });
        return list.map((suffix) => {
            return {
                label: suffix,
                value: suffix
            };
        });
    });
}

interface DateObject {
    year: number;
    month: number;
    day: number;
    week: number;
    weekArr: string[];
    hour: string;
    minute: string;
    second: string;
}

const getDate = (date: Date): DateObject => {
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let week = date.getDay();
    let weekArr = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
    let hour:any = date.getHours();
    hour = hour < 10 ? "0" + hour : hour.toString();
    let minute:any = date.getMinutes();
    minute = minute < 10 ? "0" + minute : minute.toString();
    let second:any = date.getSeconds();
    second = second < 10 ? "0" + second : second.toString();
    return {
        year: year,
        month: month,
        day: day,
        week: week,
        weekArr: weekArr,
        hour: hour,
        minute: minute,
        second: second
    }
}

const iOSResize = () => {
    const u = navigator.userAgent;
    const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    let flag: boolean, toScroll: any;
    if (isiOS) {
        console.info('ios');
        let viewportmeta:any = document.querySelector('meta[name="viewport"]');

        document.body.addEventListener('focusin', () => {
            flag = true;
            clearTimeout(toScroll);
            if (viewportmeta) {
                viewportmeta.content = 'width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no';
            }
        });

        document.body.addEventListener('focusout', () => {
            flag = false;
            if (!flag) {
                if (viewportmeta) {
                    viewportmeta.content = 'width=device-width, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes';
                }
                toScroll = setTimeout(() => {
                    // window.scrollTo({ top: 0, left: 0, behavior: "smooth" })
                }, 20);
            }
        });
    } else {
        console.info("android");
    }
}

const setMeta = (viewportmeta: HTMLMetaElement | null) => {
    if (viewportmeta) {
        document.body.addEventListener('focusin', function () {
            viewportmeta.content = 'width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no';
        });
        document.body.addEventListener('focusout', function () {
            viewportmeta.content = 'width=device-width, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes';
        });
    }
}
const Window:any = window
const windowOnResize = () => {
    let myLayout = {
        height: 0,
        width: 0
    };
    return new Promise((resolve) => {
        window.onresize = () => {
            Window.fullHeight = document.documentElement.clientHeight;
            Window.fullWidth = document.documentElement.clientWidth;
            myLayout.height = Window.fullHeight;
            myLayout.width = Window.fullWidth;
            resolve(myLayout);
        };
    });
}

const rand = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min)) + min;
}

const getNumber = () => {
    let date = new Date();
    let randomNum = rand(1000, 9999);
    let month = (date.getMonth() + 1);
    let monthStr = month < 10 ? '0' + month : month.toString();
    let day = date.getDate();
    let dayStr = day < 10 ? '0' + day : day.toString();
    let number = date.getFullYear() + '' + monthStr + '' + dayStr + '' + randomNum;
    return number;
}

const getDecodeStr = (str: string) => {
    return decode(str);
}

const getJsonObj = (str: string) => {
    return JSON.parse(str);
}

const getJWTStr = (str: string) => {
    let firstIndex = str.indexOf('}') + 1;
    let secondIndex = str.indexOf('}', firstIndex) + 1;
    return str.slice(firstIndex, secondIndex);
}

const getFormatDate = (date: DateObject) => {
    let month = date.month < 10 ? '0' + date.month : date.month;
    let day = date.day < 10 ? '0' + date.day : date.day;
    return date.year + '-' + month + '-' + day;
}

const getFormatTime = (date: DateObject) => {
    return date.hour + ':' + date.minute + ':' + date.second;
}

const getFormatTimeWithoutSecond = (date: DateObject) => {
    return date.hour + ':' + date.minute;
}

const getFormatDateTime = (dateStr: string) => {
    let newDate = new Date(dateStr);
    let date = getDate(newDate);
    let formatDate = getFormatDate(date);
    let formatTime = getFormatTime(date);
    return formatDate + ' ' + formatTime;
}

const readImg = (file: File) => {
    return new Promise<HTMLImageElement>((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();
        reader.onload = function (e) {
            img.src = e.target?.result as string;
        };
        reader.onerror = function (e) {
            reject(e);
        };
        reader.readAsDataURL(file);
        img.onload = function () {
            resolve(img);
        };
        img.onerror = function (e) {
            reject(e);
        };
    });
};

const compressImg = (img: HTMLImageElement, type: string, mx: number, mh: number) => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    const { width: originWidth, height: originHeight } = img;
    const maxWidth = mx;
    const maxHeight = mh;
    let targetWidth = originWidth;
    let targetHeight = originHeight;

    if (originWidth > maxWidth || originHeight > maxHeight) {
        if (originWidth / originHeight > 1) {
            targetWidth = maxWidth;
            targetHeight = Math.round(maxWidth * (originHeight / originWidth));
        } else {
            targetHeight = maxHeight;
            targetWidth = Math.round(maxHeight * (originWidth / originHeight));
        }
    }

    canvas.width = targetWidth;
    canvas.height = targetHeight;
    if (context) {
        context.clearRect(0, 0, targetWidth, targetHeight);
        context.drawImage(img, 0, 0, targetWidth, targetHeight);
    }
    return canvas.toDataURL(type || "image/jpeg", 0.5);
};

const dataURLtoFile = (dataurl: string, filename: string): File => {
    const arr = dataurl.split(",");
    const mime = arr[0].match(/:(.*?);/)![1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
}

const randomNum = () => {
    let Num = "";
    for (let i = 0; i < 13; i++) {
        Num += Math.floor(Math.random() * 10);
    }
    return Num;
}

const parseCurrency = (input: string): number | null => {
    const nums = input.replace(/(,|¥|\s)/g, "").trim();
    if (/^\d+(\.(\d+)?)?$/.test(nums))
        return Number(nums);
    return nums === "" ? null : NaN;
}

const formatCurrency = (value: number | null): string => {
    if (value === null)
        return "";
    return `${value.toLocaleString("zh-CN")} ¥`;
}

const isArray = (arr: any): boolean => {
    if (Array.isArray) {
        return Array.isArray(arr);
    } else {
        return Object.prototype.toString.call(arr) === '[object Array]';
    }
}

const digitUppercase = (price: number | string): string => {
    if (typeof price === 'string' && price.includes('*')) {
        return '*元*角*分';
    }

    const fraction = ['角', '分'];
    const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
    let num = Math.abs(Number(price));
    let s = '';

    fraction.forEach((item, index) => {
        s += (digit[Math.floor(num * 10 * (10 ** index)) % 10] + item).replace(/零./, '');
    });

    s = s || '整';
    num = Math.floor(num);

    for (let i = 0; i < unit[0].length && num > 0; i += 1) {
        let p = '';
        for (let j = 0; j < unit[1].length && num > 0; j += 1) {
            p = digit[num % 10] + unit[1][j] + p;
            num = Math.floor(num / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }

    return s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}

const getUrlKey = (name: string, url: string): string | null => {
    return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || ["", ""])[1].replace(/\+/g, '%20')) || null;
}

export {
    isAndroid, getDate, getFormatDate, getFormatTime,
    windowOnResize, getJsonObj, getDecodeStr, getNumber, getFormatDateTime,
    readImg, compressImg, dataURLtoFile, randomNum, getOptions,
    parseCurrency, formatCurrency, digitUppercase, getFormatTimeWithoutSecond, iOSResize, isArray, getUrlKey, getPermission, checkOperatingSystem
}
   
