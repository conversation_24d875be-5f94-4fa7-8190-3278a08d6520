// APIUtils.ts
import CryptoJS from 'crypto-js';
import { doPostAsync, doPutAsync, doGetAsync } from './AxiosUtil'; // 确保路径正确
import { convertDateToTimestamp } from './xUtils'; // 确保路径正确
// import { IconSearch } from '@arco-design/web-vue/es/icon';
import { Search as IconSearch } from '@vicons/ionicons5'
import { h } from 'vue';


interface ColorInfo {
    color: string;
    borderColor: string;
    textColor: string;
}
const colors: ColorInfo[] = [
    { color: '#DEB887', borderColor: '#A52A2A', textColor: '#FFFFFF' },
    { color: '#FFA07A', borderColor: '#FF4500', textColor: '#FFFFFF' },
    { color: '#4682B4', borderColor: '#000080', textColor: '#FFFFFF' },
    { color: '#98FB98', borderColor: '#008000', textColor: '#006400' },
    { color: '#90EE90', borderColor: '#2E8B57', textColor: '#FFFFFF' },
    { color: '#F4A460', borderColor: '#D2691E', textColor: '#FFFFFF' },
    { color: '#ADD8E6', borderColor: '#5F9EA0', textColor: '#2F4F4F' },  // 浅蓝色与军蓝色的边框
    { color: '#FFD700', borderColor: '#FF8C00', textColor: '#8B4513' },  // 金色与深橙色边框
    { color: '#E6E6FA', borderColor: '#8A2BE2', textColor: '#4B0082' },  // 淡紫罗兰色与靛蓝色边框
    { color: '#FFC0CB', borderColor: '#DB7093', textColor: '#C71585' },  // 粉红色与中紫红色边框
    { color: '#B0C4DE', borderColor: '#4682B4', textColor: '#000080' },  // 亮钢蓝色与海军蓝色边框
    { color: '#3CB371', borderColor: '#2E8B57', textColor: '#006400' }   // 春绿色与海洋绿色边框
];


// 生成一个静态的 salt，也可以每次启动应用时随机生成
const salt = ref(generateSalt(16));

// 生成随机 salt 的函数
function generateSalt(length: number = 16): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}
// 映射每个 unique str 到它的 salt
const saltMap = new Map<string, string>();

// 散列函数
// Hash function to convert a string to a color index
function hashString(str: string): number {
    // if (!str) {
    //     return 0;  // 如果是undefined或空字符串，返回默认索引
    // }
    let salt = saltMap.get(str);
    if (!salt) {
        salt = generateSalt();
        saltMap.set(str, salt);  // 存储 salt 以便后续使用相同的 salt
    }
    str = salt + str;  // 将 salt 添加到字符串前
    let hash = 0;

    for (let i = 0; i < str.length; i++) {
        const character = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + character;
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash) % colors.length;  // 使用模运算确保索引在数组长度内
}


// Computed property to get the color based on internalValue
// const colorObject = computed(() => {
//     const index = hashString(internalValue.value || "");  // Use an empty string as a default
//     console.log(`output->index`, index)
//     return colors[index];
// });

export function getColorObj(params: string): ColorInfo {
    const index = hashString(params);
    return colors[index];
}

export async function getPermission(params: string): Promise<number | undefined> {
    try {
        const url = '/aliapi/auth/fe/permission/fyapp?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
        const res: any = await doGetAsync(url, undefined, undefined);
        if (res.code === '00000') {
            const elems = res.data.elem;
            const permissionPrefix = 'permission_id_';
            for (const elem of elems) {
                if (elem.code && elem.code.startsWith(permissionPrefix)) {
                    const permissionIdStr = elem.code.substring(permissionPrefix.length);
                    const permissionId = Number(permissionIdStr);
                    if (!isNaN(permissionId)) {
                        return permissionId; // 返回第一个匹配的permission_id后面的数值作为number
                    } else {
                        console.warn(`Invalid permission ID: ${permissionIdStr}`);
                    }
                }
            }
        }
        return undefined; // 如果没有找到匹配的返回undefined
    } catch (error) {
        console.error('Error fetching data:', error);
        return undefined;
    }
}

export async function fetchPermission(url: string, params: any) {
    try {
        const response: any = await doPostAsync(url, params);
        if (response.code === 200) {

            if (Array.isArray(response.result)) {
                return response.result.map((item: any) => ({
                    ...item
                }));
            }
        } else {
            console.error('Failed to fetch data:', response.msg);
            return [];
        }
        return [];
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}

// 通用获取数据函数
export async function fetchData(url: string, params: any) {
    try {
        const response: any = await doPostAsync(url, params);
        if (response.code === 200) {
            // 检查 response.result 是否为数组
            if (Array.isArray(response.result)) {
                return response.result.map((item: any) => ({
                    ...item,
                    createDate: item.createDate ? convertDateToTimestamp(item.createDate) : undefined,
                    updateDate: item.updateDate ? convertDateToTimestamp(item.updateDate) : undefined
                }));
            } else { // 如果 response.result 是单个对象
                const item = response.result;
                return [{
                    ...item,
                    createDate: item.createDate ? convertDateToTimestamp(item.createDate) : undefined,
                    updateDate: item.updateDate ? convertDateToTimestamp(item.updateDate) : undefined
                }];
            }
        } else {
            console.error('Failed to fetch data:', response.msg);
            return [];
        }
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}

export async function fetchDataAlt(url: string, params: any) {
    try {
        const response: any = await doPostAsync(url, params);
        if (response.code === 200) {
            // 检查 response.result 是否为数组
            if (Array.isArray(response.data)) {
                return response.data.map((item: any) => ({
                    ...item
                }));
                
            } else { // 如果 response.result 是单个对象
                const item = response.data;
                return [{
                    ...item
                }];
            }
        } else {
            console.error('Failed to fetch data:', response.msg);
            return [];
        }
    } catch (error) {
        console.error('Error fetching data:', error);
        return [];
    }
}

// 通用删除数据函数
export async function deleteData(url: string, id: number) {
    try {
        const response: any = await doPutAsync(url, { id });
        if (response.code === 200) {
            console.info('Deleted successfully:', response);
            return true;
        } else {
            console.error('Failed to delete:', response.msg);
            return false;
        }
    } catch (error) {
        console.error('Error deleting data:', error);
        return false;
    }
}

export async function deleteById(url: string, requestParams?: any) {
    if (!requestParams.id) {
        return false;
    }

    // 弹出确认对话框
    if (!window.confirm('您确定要删除吗？')) {
        // 如果用户点击了取消，则不执行删除操作
        return false;
    }

    try {
        const response: any = await doPutAsync(url, requestParams);
        if (response.code === 200) {
            return true;
        } else {
            return false;
        }
    } catch (error) {
        console.error(error);
        return false;
    }
}
// export async function saveData(url: string, rowData: any) {
//     const dataToSend = { ...rowData };

//     try {
//         const response: any = await doPutAsync(url, dataToSend);
//         if (response.code === 200) {
//             const updatedRow = {

// }

// 通用保存数据函数
export async function saveData(url: string, rowData: any, rowIndex: number, permissionId: number) {
    const dataToSend = { ...rowData };
    if (dataToSend.id && dataToSend.id < 0) {
        delete dataToSend.id;
    }
    dataToSend.permissionId = permissionId; // Assume this needs to be set for all save operations

    try {
        const response: any = await doPutAsync(url, dataToSend);
        if (response.code === 200) {
            const updatedRow = {
                ...rowData,
                ...response.result,
                editing: false
            };
            if (updatedRow.createDate) {
                updatedRow.createDate = convertDateToTimestamp(updatedRow.createDate);
            }
            if (updatedRow.updateDate) {
                updatedRow.updateDate = convertDateToTimestamp(updatedRow.updateDate);
            }
            return { success: true, updatedRow, rowIndex };
        } else {
            console.error('Failed to save data:', response.msg);
            return { success: false };
        }
    } catch (error) {
        console.error('Error saving data:', error);
        return { success: false };
    }
}

// 定义 TypeScript 接口用于类型安全
interface OSSDeleteResponse {
    code: number;
    message: string;
}
// 定义函数，使用 async 和返回 Promise<OSSDeleteResponse> 来处理异步操作和错误
export const ossDelete = async (key: string, ossSchema: string): Promise<OSSDeleteResponse> => {
    const url = '/fyschedule/oss/delete';
    const params = {
        key: key,
        bucket: ossSchema
    };

    try {
        const response: any = await doPostAsync(url, params);
        // 假设 doPostUnencoded 正确地处理了网络请求并返回了 JSON 对象
        if (response.status >= 200 && response.status < 300) {
            // 成功删除
            return { code: 0, message: 'File successfully deleted from OSS.' };
        } else {
            // 服务器返回了错误状态
            return { code: 1, message: `Failed to delete file: ${response.statusText}` };
        }
    } catch (error: any) {
        // 网络或其它错误
        return { code: 1, message: `Error during OSS file deletion: ${error.message || error}` };
    }
}

// 序列化方法
const replacer = (key: string, value: any) => {
    if (typeof value === 'function') {
        return value.toString();
    }
    return value;
};

const serializeColumns = (columns: any) => {
    return JSON.stringify(columns, replacer);
};

// 反序列化方法
const reviver = (key: string, value: any) => {
    if (typeof value === 'string' && value.startsWith('function')) {
        return new Function('return ' + value)();
    }
    return value;
};

const deserializeColumns = (jsonString: string) => {
    const parsed = JSON.parse(jsonString, reviver);

    // 恢复图标函数
    parsed.forEach((col: any) => {
        if (col.filterable && col.filterable.icon) {
            col.filterable.icon = () => h(IconSearch);
        }
    });

    return parsed;
};

// 确保密钥长度为16字节
const secretKey = 'xlhlhd0000000000';

if (!secretKey) {
    throw new Error("Secret key is not defined");
}

/**
 * 加密ID
 * @param {number | string | null | undefined} id - 要加密的ID
 * @returns {string} - 加密后的ID字符串
 */
export function encryptId(id: number | string | null | undefined): string {
    if (id === null || id === undefined || id === '') {
        throw new Error("ID cannot be null, undefined or an empty string");
    }

    const idString = id.toString();
    const encrypted = CryptoJS.AES.encrypt(idString, CryptoJS.enc.Utf8.parse(secretKey), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
}
/**
 * 解密ID
 * @param {string} encryptedId - 加密的ID字符串
 * @returns {string} - 解密后的ID字符串
 */
export function decryptId(encryptedId: string): string {
    const decrypted = CryptoJS.AES.decrypt(encryptedId, CryptoJS.enc.Utf8.parse(secretKey), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
}

export function encryptRowId(rowId: number): string {
    try {
        const ciphertext = CryptoJS.AES.encrypt(rowId.toString(), secretKey).toString();
        return ciphertext.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    } catch (error) {
        console.error('加密失败:', error);
        return '';
    }
}

export function decryptRowId(encryptedId: string): number {
    try {
      const formattedId = encryptedId.replace(/-/g, '+').replace(/_/g, '/');
      const bytes = CryptoJS.AES.decrypt(formattedId, secretKey);
      const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
  
      // 尝试将解密后的字符串转换为数字
      const rowId = Number(decryptedString);
      if (isNaN(rowId)) {
        throw new Error('解密后的值不是有效的数字');
      }
  
      return rowId;
    } catch (error) {
      console.error('解密失败:', error);
      return -1; // 返回一个合适的默认值，表示解密失败
    }
  }

// 辅助函数：将字段名转换为驼峰命名法
function toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase());
}
// const lookupTable = computed(() => {
//     const map = new Map();
//     permissionList.forEach(item => {
//         const key = `${item.fieldName?.trim()}|${item.tableName}`;
//         map.set(key, item.writable);
//     });
//     return map;
// });

// function findWritable(fieldName: string, tableName: string): boolean {
//     const key = `${fieldName.trim()}|${tableName}`;
//     return lookupTable.value.get(key) ?? false;
// }


// 示例用法
// (async () => {
//     await getPermissionList(99);
//     const fieldName = "is_prefabricated";
//     const tableName = "fyjs_project_management";
//     const writable = findWritable(fieldName, tableName);
//     console.log(`Writable: ${writable}`);
// })();
export {
    serializeColumns, deserializeColumns, toCamelCase,
}
export type {
    ColorInfo
}