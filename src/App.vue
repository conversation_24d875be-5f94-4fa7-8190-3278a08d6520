<script setup lang="ts">
import { RouterView } from 'vue-router';
import { NConfigProvider } from 'naive-ui'
import { zhCN, dateZhCN } from 'naive-ui'
defineOptions({
  name: 'FygAccountingProjectList'
})
</script>

<template>
  <n-config-provider :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <!-- <RouterView></RouterView> -->
      <router-view v-slot="{ Component }">
        <keep-alive :include="['FygAccountingProjectList']">
          <component :is="Component"/>
        </keep-alive>
      </router-view>
    </n-message-provider>
  </n-config-provider>
</template>

<style scoped></style>
