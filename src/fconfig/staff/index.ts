type MyData = {
    id: number | null,
    creator: string | null,
    creatorName: string | null,
    lastEditor: string | null,
    dataStatus: number | null,
    createDate: number | null,
    staffName: string | undefined,
    staffIdCard: string | undefined,
    gender: number | undefined,
    age: number | undefined,
    birth: number | undefined,
    householdRegistration: string | undefined,
    householdRegistrationType: string | undefined,
    academicCareer: string | undefined,
    politicsStatus: string | undefined,
    phoneNumber: string | undefined,
    entryDate: number | undefined,
    dateBecomeRegular: number | undefined,
    belongTo: string | undefined,
    ageInFy: number | undefined,
    staffProperty: string | undefined,
    isOnPost: number | undefined,
    staffRemarks: string | undefined,
    avatar?: string,
    eas?: string
}

type QueryParam = {
    dataStatus: number | null;
    staffName?: string;
    staffIdCard?: string;
    gender?: number;
    ageStart?: number;
    ageStop?: number;
    birthStart?: number;
    birthStop?: number;
    householdRegistration?: string;
    householdRegistrationType?: string;
    academicCareer?: string;
    politicsStatus?: string;
    phoneNumber?: string;
    entryDateStart?: number;
    entryDateStop?: number;
    dateBecomeRegularStart?: number;
    dateBecomeRegularStop?: number;
    belongTo?: string;
    ageInFyStart?: number;
    ageInFyStop?: number;
    staffProperty?: string;
    isOnPost?: number;
    staffRemarks?: string;
    [key: string]: any; // Add an index signature
};

export type {
    MyData, QueryParam
}