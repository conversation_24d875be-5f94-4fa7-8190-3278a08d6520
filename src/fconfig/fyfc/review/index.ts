type UserRole = 'unknown' | 'employee' | 'colleague' | 'manager' | 'admin';

// 1. 定义基础类型和映射关系
type EvaluationStatus = 'self' | 'colleague' | 'manager' | 'completed';

// 类型安全的映射对象（必须包含所有状态）
const statusMap: Record<EvaluationStatus, string> = {
  self: '自评阶段',
  colleague: '同事评价',
  manager: '主管审核',
  completed: '已完成'
} as const;

// 2. 辅助函数实现
const getStatusLabel = (status: EvaluationStatus): string => {
  return statusMap[status];
};

type Evaluation = {
    id?: number;
    department?: string;
    name?: string;
    reviewDate?: number;
    additionalScore?: number; //线上转发
    score?: number; //最终得分
    comment?: string; //说明
    createdAt?: number;
    createdBy?: string;
    updatedBy?: string;
    status?: string;
}

type EvaluationScore = {
  id?: number;
  evaluationId?: number;
  evaluator?: string;
  type: UserRole;
  performanceScore?: number; //绩效得分
  attitudeScore?: number; //态度得分
  abilityScore?: number; //能力得分
  growthScore?: number; //个人成长得分
  score?: number; //小计
  signature?: string;
}

type EvaluationStatusHistory = {
  id?: number;
  evaluationId?: number;
  previousStatus?: EvaluationStatus;
  newStatus: EvaluationStatus;
  changedBy: string;
  changedAt: number;
}

export type {
  Evaluation,
  EvaluationStatus,
  EvaluationScore,
  EvaluationStatusHistory,
  UserRole
}

export {
  getStatusLabel
}

