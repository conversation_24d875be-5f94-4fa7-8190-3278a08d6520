type OssItem = {
    uid: number,
    name: string,
    fileName: string,
    url: string
}

type Item = {
    id: number | undefined,
    parentId: number | null,
    name: string | null,
    type: string | null,
    content: string | null,
    label: string | null,
    createDate: number | null,
}

type CpUser = {
    userId: string | undefined,
    userName: string | undefined,
    isTenderer: false | true,
    isOpener: false | true,
    isAdmin: false | true,
    avatar: string | undefined
}

type MessageCard = {
    agentId: number | undefined,
    toUser: string | undefined,
    sourceDesc: string | undefined,
    actionMenuDesc: string | undefined,
    mainTitleTitle: string | undefined,
    mainTitleDesc: string | undefined,
    cardButtonDesc: string | undefined,
    actionButtonDesc: string | undefined,
    url: string | undefined
}

type FyjsOption = {
    id: number | undefined,
    parentId: number | undefined, 
    optionKey: string | undefined,
    optionLabel: string | undefined,
    optionValue: string | undefined,
    
}

interface Props {
    id?: number,
    creator?: string,
    creatorName?: string,
    currentUser?: string,
    voucherId?: number,
    permissionId?: number,
}
interface Permission {
    tableName?: string,
    fieldName?: string,
    writable?: boolean
}
const formatter = (value:any) => {
    const values = value.split('.');
    values[0] = values[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return values.join('.')
};

const parser = (value:any) => {
    
    return value.replace(/,/g, '')
};

export type {
    OssItem, Item, CpUser, MessageCard, Props, Permission
}

export {
    formatter, parser,
}
