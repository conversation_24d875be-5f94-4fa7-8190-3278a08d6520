// filter.ts
import { ref, computed, isRef, ComputedRef, Ref, h } from 'vue';
import { DataTableColumn, DataTableColumns, DataTableBaseColumn } from 'naive-ui';
import Filter from '../../../../components/Filter.vue'; // 确保你有一个Filter.vue组件

type FilterField = Record<string, any>; // 你需要根据实际需求调整这个类型定义
type SetFilter = (key: string, value: any, valuesRef: Ref<Record<string | number, any>>) => void;
type FilterVal = any;

interface UseFilterProps {
  fields: FilterField;
  filter: SetFilter;
  columns: DataTableColumns<any>;
}

export function useFilter(props: UseFilterProps | ComputedRef<UseFilterProps> | Ref<UseFilterProps>) {
    let fields: FilterField;
    let filter: SetFilter;
    let columns: DataTableColumns<any>;
    let values = ref<Record<string | number, FilterVal>>({});

    return {
      columns: computed(() => {
        const trueRef = isRef(props);
        fields = trueRef ? props.value.fields : props.fields;
        filter = trueRef ? props.value.filter : props.filter;
        columns = trueRef ? props.value.columns : props.columns;
        values.value = {};
        return columns.map(col => parseFilter(col, fields, filter, values));
      }),
      values
    };
}

// export function parseFilter(col: DataTableColumn<any>, fields: FilterField, setFilter: SetFilter, valuesRef: Ref<Record<string | number, FilterVal>>): DataTableColumn<any> {
//     const key = col.key as string;
//     let column: DataTableBaseColumn<any> = {...col as DataTableBaseColumn<any>};

//     if (fields[key] !== undefined) {
//       column.filter = fields[key].filter || (fields[key].type === 'text' ? 'default' : (val, data) => dateFilter(val, data[key], dateValueFormatByMoment[fields[key].type as keyof typeof dateValueFormatByMoment], fields[key].type as DatePickerType));
//       column.filterMode = fields[key].filterMode || 'or';
//       column.filterMultiple = fields[key].filterMultiple !== undefined ? fields[key].filterMultiple : true;
//       column.renderFilterMenu = ({ hide }) => {
//         return h(Filter, {
//           hide,
//           type: fields[key].type,
//           value: valuesRef.value[key],
//           onFilter: (val: FilterVal) => {
//             valuesRef.value[key] = val;
//             setFilter(key, val, valuesRef);
//             hide();
//           }
//         });
//       };
//     }
//     return column;
// }

export function parseFilter(col: any, fields: FilterField, setFilter: SetFilter, valuesRef: Ref<Record<string | number, FilterVal>>): DataTableColumn<any> {
  const key = col.key;
  
  // 确保 key 是 string 类型
  if (typeof key !== 'string') {
      throw new Error('Column key must be a string');
  }

  let column: DataTableBaseColumn<any> = {...col};

  if (fields[key] !== undefined) {
      const defaultFilter = (val: any, data: any) => null;
      column.filter = fields[key].filter || (fields[key].type === 'text' ? 'default' : defaultFilter);
      column.filterMode = fields[key].filterMode || 'or';
      column.filterMultiple = fields[key].filterMultiple !== undefined ? fields[key].filterMultiple : true;
      column.renderFilterMenu = ({ hide }) => {
          return h(Filter, {
              hide,
              type: fields[key].type,
              value: valuesRef.value[key],
              onFilter: (val: FilterVal) => {
                  valuesRef.value[key] = val;
                  setFilter(key, val, valuesRef);
                  hide();
              }
          });
      };
  }
  return column;
}
