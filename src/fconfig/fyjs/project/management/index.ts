import { Search as IconSearch, } from '@vicons/ionicons5'
import { h } from 'vue';
import {
    affiliatedCompanyData, taxationMethodData, projectProgressData, projectStatusData,
    projectSourceData, completionSettlementStatusData, performanceGuaranteeMethodData, controlLevelData,
    contractTypeData, internalOperationalContractingModelData,
    natureOfPartyAData, projectNatureAData, projectNatureBData, projectTypeData,
    projectPurposeAData, projectPurposeBData, projectPurposeCData, projectPurposeDData,
    structuralTypeData, projectNatureCData, projectPurposeEData
} from "../../../../fconfig/fyjs/project/management/options";
import { NButton, NGi, NGrid, NIcon, NInput, NSpace, NTag } from 'naive-ui';
import {
    DataTableColumns,
    DataTableBaseColumn,
    DataTableFilterState
} from 'naive-ui'
import MyFilter from '../../../../components/MyFilterComponent.vue';
import MyDatePicker from '../../../../components/MyDateFilterComponent.vue'
import MyNumberPicker from '../../../../components/MyNumberFilterComponent.vue'

const optionsMap = {
    affiliatedCompany: affiliatedCompanyData,
    taxationMethod: taxationMethodData,
    projectProgress: projectProgressData,
    projectStatus: projectStatusData,
    projectSource: projectSourceData,
    completionSettlementStatus: completionSettlementStatusData,
    performanceGuaranteeMethod: performanceGuaranteeMethodData,
    controlLevel: controlLevelData,
    contractType: contractTypeData,
    internalOperationalContractingModel: internalOperationalContractingModelData,
    natureOfPartyA: natureOfPartyAData,
    projectNatureA: projectNatureAData,
    projectNatureB: projectNatureBData,
    projectType: projectTypeData,
    projectPurposeA: projectPurposeAData,
    projectPurposeB: projectPurposeBData,
    projectPurposeC: projectPurposeCData,
    projectPurposeD: projectPurposeDData,
    structuralType: structuralTypeData,
    projectNatureC: projectNatureCData,
    projectPurposeE: projectPurposeEData, 
    singleOrGroup: [{
        label: '单体',
        value: '0',
    }, {
        label: '群体',
        value: '1',
    }]
};


type FyjsProjectManagement = {
    id?: number; // 记录的唯一标识符
    projectDefinitionId?: string; // 项目定义的标识符
    projectDefinition?: string; // 项目定义的描述
    isMainProject?: number; //是否主项目
    mainProjectId?: number; // 本记录所属主项目的标识符
    affiliatedCompanyKey?: string; // 所属公司选项的键
    affiliatedCompany?: string; // 所属公司的名称
    constructionUnit?: string; // 建设单位的名称
    constructionPermitId?: string; // 施工许可证的标识符
    constructionPermitNumber?: string; // 施工许可证编号
    constructionPermitName?: string; // 施工许可证名称
    projectName?: string; // 项目名称
    projectAbbreviation?: string; // 项目简称
    managementRepresentationId?: number; // 管理代表id
    managementRepresentation?: string; // 管理代表
    managementRepresentationAvatar?: string; // 管理代表头像
    siteManagerId?: number; // 现场负责人id
    siteManager?: string; // 现场负责人
    siteManagerAvatar?: string; // 现场负责人头像
    siteManagerContact?: string; // 现场负责人联系电话
    businessManagerId?: number; // 经营负责人id
    businessManager?: string; // 经营负责人
    businessManagerAvatar?: string; // 经营负责人头像
    businessManagerContact?: string; // 经营负责人联系电话
    projectManagerId?: number; // 项目经理id
    projectManager?: string; // 项目经理
    projectManagerAvatar?: string; // 项目经理头像
    projectManagerContact?: string; // 项目经理联系电话
    bidContractPrice?: number; // 中标合同价（万元）
    taxationMethod?: string; // 计税方式
    taxationMethodKey?: string; // 计税方式键
    projectStatus?: string; // 项目状态
    projectStatusKey?: string; // 项目状态键
    projectProgress?: string; // 项目进度
    projectProgressKey?: string; // 项目进度键
    completionSettlementStatus?: string; // 竣工结算状态
    completionSettlementStatusKey?: string; // 竣工结算状态键
    projectSource?: string; // 项目来源
    projectSourceKey?: string; // 项目来源键
    performanceGuaranteeMethod?: string; // 履约保证方式
    performanceGuaranteeMethodKey?: string; // 履约保证方式键
    performanceGuaranteeAmount?: number; // 履约保证金(元)
    bidNotificationName?: string; // 中标通知书名称
    bidDate?: string | Date | number; // 中标日期
    internalApprovalDate?: string | Date | number; // 内部立项日期
    contractSigningDate?: string | Date | number; // 合同签订日期
    contractCommencementDate?: string | Date | number; // 合同开工日期
    contractCompletionDate?: string | Date | number; // 合同竣工日期
    contractDuration?: number; // 合同工期(天)
    plannedCompletionDate?: string | Date | number; // 计划竣工日期
    commencementReportDate?: string | Date | number; // 开工报告日期
    actualStartDate?: string | Date | number; // 实际开工日期
    actualCompletionDate?: string | Date | number; // 实际完工日期
    completionDate?: string | Date | number; // 竣工日期
    completionAcceptanceDate?: string | Date; // 竣工验收日期
    controlLevel?: string; // 管控等级
    controlLevelKey?: string; // 管控等级键
    contractType?: string; // 承包类型
    contractTypeKey?: string; // 承包类型键
    internalOperationalContractingModel?: string; // 内部经营承包模式
    internalOperationalContractingModelKey?: string; // 内部经营承包模式键
    projectOverview?: string; // 工程概况
    contractingScope?: string; // 承包范围
    subcontractingSituation?: string; // 分包情况
    renderingImage?: string; // 效果图
    province?: string; // 所在省
    city?: string; // 所在市
    district?: string; // 所在区县
    detailedAddress?: string; // 详细地址
    gps?: string; // GPS坐标
    videoSurveillanceKey?: string; // 视频监控键
    videoSurveillance?: string; // 视频监控
    natureOfPartyAKey?: string; // 甲方性质键
    natureOfPartyA?: string; // 甲方性质
    projectTypeKey?: string; // 工程类型键
    projectType?: string; // 工程类型
    projectNatureKey?: string; // 工程性质键
    projectNature?: string; // 工程性质
    projectPurposeKey?: string; // 工程用途键
    projectPurpose?: string; // 工程用途
    structuralTypeKey?: string; // 结构类型键
    structuralType?: string; // 结构类型
    municipalSupport?: number; // 市政配套
    municipalSupportProject?: string; // 市政配套项目
    municipalSupportProjectId?: number; // 市政配套项目标识符
    totalBuildingArea?: number; // 总建筑面积(M2)
    aboveGroundArea?: number; // 地上面积(M2)
    belowGroundArea?: number; // 地下面积(M2)
    totalNumberOfFloors?: number; // 总层数
    numberOfAboveGroundFloors?: number; // 地上层数
    numberOfBelowGroundFloors?: number; // 地下层数
    buildingHeight?: number; // 建筑高度(米)
    foundationPitDepth?: number; // 基坑深度(米)
    singleOrGroup?: number; // 单体或群体建筑标志，0表示单体，1表示群体
    isPrefabricated?: number; // 是否装配 1钢结构装配 2 预制构件装配 否
    prefabricationRate?: number; // 装配率
    includesSteelStructure?: number; // 是否包含钢结构，1表示是，0表示否
    steelStructurePart?: string; // 钢结构部分的描述
    maximumSpanOfSteelStructure?: number; // 钢结构最大跨度(米)
    isFullyFurnished?: number; // 是否全装修，1表示是，0表示否
    isPrefabricatedDecoration?: number; // 是否采用装配式装修，1表示是，0表示否
    isSmartConstruction?: number; // 是否智能建造，1表示是，0表示否
    isSmartSite?: number; // 是否智慧工地，1表示是，0表示否
    isBim?: number; // 是否应用BIM技术，1表示是，0表示否
    spanInMeters?: number; // 跨度(米)
    others?: string; // 其他信息
    landArea?: number; // 用地面积
    createDate?: string | Date | number; // 记录创建时间
    creator?: string; // 记录的创建者
    lastEditor?: string; // 记录的最后编辑者
    dataStatus?: number; // 数据状态（用于软删除或标记记录状态）
    [key: string]: any;
};

type FyjsProjectRelatedUnitsDTO = {
    id?: number; // Optional field, can be undefined
    parentId?: number; // Optional field, can be undefined
    unitName?: string;
    assumedRole?: string;
    assumedRoleKey?: string;
    personInChargeName?: string;
    projectLeaderIdNumber?: string;
    personId?: number;
    contactPhone?: string;
    creator?: string;
    lastEditor?: string;
    createDate?: number; // Optional field, using number for timestamps
    dataStatus?: number;
    editing?: boolean;
    permissionId?: number;
};


type MainProject = {
    id?: number,
    projectName?: string,
}

type QueryParam = {
    dataStatus?: number,
    projectName?: string,
    currentPage?: number,
    pageSize?: number,
    order?: string,
    columnKey?: string,
    staffName?: string,
    permissionId?: number,
    [key: string]: any
}

interface MyOption {
    label?: string,
    value?: any,
    data?: any
}

type FyjsProjectManagementPersonnelDTO = {
    id?: number;  // 记录的唯一标识符
    parentId?: number;  // 上级项目或人员的标识符
    personId?: number;  // 人员标识符
    positionKey?: string;  // 职位键
    position?: string;  // 职位描述
    name?: string;  // 名称
    contactNumber?: string;  // 联系电话
    remarks?: string;  // 备注
    createDate?: number;  // 记录创建时间的时间戳
    // updateDate?: number;  // 记录最后更新时间的时间戳（如果需要可以取消注释）
    creator?: string;  // 记录的创建者
    lastEditor?: string;  // 记录的最后编辑者
    dataStatus?: number;  // 数据状态（用于软删除或标记记录状态）
    editing?: boolean;
    permissionId?: number;
    avatar?: string;
    [key: string]: any;
};

type FyjsProjectRecordedPersonnelDTO = {
    id?: number;  // 员工的唯一标识符
    parentId?: number;  // 上级人员或项目的标识符
    position?: string;  // 职位描述
    positionKey?: string;  // 职位关键字或代码
    personId?: number;  // 人员的唯一标识符
    name?: string;  // 人员姓名
    contactNumber?: string;  // 联系电话
    recordStatus?: number;  // 记录的状态
    createDate?: number;  // 创建日期，使用时间戳
    // updateDate?: number;  // 更新日期，使用时间戳
    creator?: string;  // 记录的创建者
    lastEditor?: string;  // 最后编辑者
    dataStatus?: number;  // 数据状态（如是否有效）
    avatar?: string;  // 用户头像
    editing?: boolean;
    permissionId?: number;
};

type FyjsProjectQualityTargetDTO = {
    id?: number;  // 记录的唯一标识符
    parentId?: number;  // 上级项目或人员的标识符
    targetCategory?: string;  // 目标类别
    targetCategoryKey?: string;  // 目标类别的键
    targetLevel?: string;  // 目标级别
    targetLevelKey?: string;  // 目标级别的键
    awardName?: string;  // 奖项名称
    targetResult?: string;  // 目标结果
    plannedDeclarationYear?: string;  // 计划申报年份
    createDate?: number;  // 创建日期，使用时间戳
    // updateDate?: number;  // 更新日期，使用时间戳，如果需要可以取消注释
    creator?: string;  // 记录的创建者
    lastEditor?: string;  // 记录的最后编辑者
    dataStatus?: number;  // 数据状态（用于软删除或标记记录状态）
    editing?: boolean;
    permissionId?: number;
};

type FyjsProjectItemDTO = {
    id?: number;              // 文件的唯一标识符
    parentId?: number;        // 父项的标识符
    type?: string;            // 文件类型
    name?: string;            // 文件名称
    content?: string;         // 在 OSS 服务器中的名称
    groupName?: string;       // 文件所属的组名
    tagName?: string;         // 文件的标签名
    creator?: string;         // 文件的创建者
    dataStatus?: number;      // 文件的数据状态
    editing?: boolean;
    permissionId?: number;
    [key: string]: any; // Add an index signature
};

interface ProjectDetailsDTO {
    baseInfos?: ProjectDetailDTO[];
    timeLimitInfos?: ProjectDetailDTO[];
    performanceInfos?: ProjectDetailDTO[];
    projectOverview?: string;
    contractingScope?: string;
    subcontractingSituation?: string;
    projectName?: string;
    projectAbbreviation?: string;
    businessManager?: string;
    businessManagerContact?: string;
    siteManager?: string;
    siteManagerContact?: string;
    projectManager?: string;
    projectManagerContact?: string;
    projectAddress?: string;
    projectProgress?: string;
    scheduleDelay?: boolean;
    controlLevel?: string;
    smartSite?: boolean;
    smartConstruction?: boolean;
    bim?: boolean;
    isMainProject?: boolean;
    mainProjectId?: number;
}

interface ProjectDetailDTO {
    label?: string;
    value?: any;
    explain?: string;
    avatar?: string;
    typographyType?: string;
    needRender?: boolean;
    activated?: boolean;
    fontSize?: string;
    moneyMark?: boolean;
    numberMark?: boolean;
    accountMark?: boolean;
    tagMark?: boolean;
    dateMark?: boolean;
    progressMark?: boolean;
    linkMark?: boolean;
    link?: string;
    number?: string;
    tagColor?: string;
    percent?: number;
    progressColor?: string;
}

interface ExtendedDataTableBaseColumn extends DataTableBaseColumn {
    isActivated: boolean;
}

type SETTING = {
    key: string,
    title: string,
    isActivated: true | false
}
const DEFAULT_SETTING:SETTING[] = [{
    key: 'projectName',
    title: '项目名称',
    isActivated: true
}, {
    key: 'contractType',
    title: '承包类型',
    isActivated: true
}, {
    key: 'singleOrGroup',
    title: '单体/群体',
    isActivated: true
}, {
    key: 'contractSigningDate',
    title: '合同签订日期',
    isActivated: true
}]

const createColumnWithTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
    const { key } = col as ExtendedDataTableBaseColumn;
    let column = reactive({
        ...col,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }: { hide: any }) => {
            return type === 'text' ? h(MyFilter, {
                hide,
                type: type,
                filterOptionValue: column.filterOptionValue,
                onFilter: (val: any) => {
                    column.filterOptionValue = val; 
                    
                    if (doFilter) {
                        doFilter({ key: key, value: val });
                    }
                }
            }) : undefined;
        }
    });
    return column;
}

const createColumnWithDatePickerTemplate = (col: ExtendedDataTableBaseColumn, type: string, doFilter?: Function) => {
    const { key } = col as ExtendedDataTableBaseColumn;
    let column = reactive({
        ...col,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }: { hide: any }) => {
            return type === 'datePicker' ? h(MyDatePicker, {
                hide,
                type: type,
                filterOptionValue: column.filterOptionValue,
                onFilter: (val: any) => {
                    column.filterOptionValue = val;
                    if (doFilter) {
                        doFilter({ key: key + 'JsonStr', value: val });
                    }
                }
            }) : undefined;
        }
    });
    return column; 
}

const createColumnWithFilterOptions = (col: ExtendedDataTableBaseColumn, optionKey: keyof typeof optionsMap) => {
    let column = reactive({
        ...col,
        filter: true,
        filterMultiple: true,
        filterOptionValues: null, 
        filterOptions: optionsMap[optionKey]
    });
    return column;
}

const createColumnWithInputFilter = (title: string, key: string, width?: number,) => {
    const column = reactive({
        title: title,
        key: key,
        width: width ? width : 200,
        filter: true,
        filterOptionValue: undefined,
        filterOptions: [
            {
                label: 'London',
                value: 'London'
            },
            {
                label: 'New York',
                value: 'New York'
            }
        ],
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) }) // 确保SearchIcon是正确导入的
        },
        renderFilterMenu: ({ hide }: any) => {
            return h(
                NSpace,
                { style: { padding: '6px' }, vertical: true },
                {
                    default: () => [
                        h(NInput, {
                            value: column.filterOptionValue,
                            onInput: (value: any) => {
                                column.filterOptionValue = value;
                            },
                            placeholder: 'Enter text to filter'
                        }),
                        h(NGrid, {
                            xGap: 80,
                            cols: 2
                        }, {
                            default: () => [
                                h(NGi, {}, {
                                    default: () => h(NButton, {
                                        size: "small",
                                        onClick: () => {
                                            column.filterOptionValue = undefined; // 清空字符串
                                            hide();
                                        }
                                    }, {
                                        default: () => "清除"
                                    })
                                }),
                                h(NGi, {}, {
                                    default: () => h(NButton, {
                                        size: "small",
                                        type: "primary",
                                        onClick: () => {
                                            hide();
                                        }
                                    }, {
                                        default: () => "查询"
                                    })
                                })
                            ]
                        }),
                    ]
                }
            )
        },
        isActivated: true,
    })
    return column;
}
export function updateFilters(emit: (event: string, ...args: any[]) => void, key: string, value: any) {
    emit('update:filters', { key, value });
}

const DEFAULT_COLUMNS: any = [{
    title: '项目名称',
    key: 'projectName',
    width: 400,
    filter: true,
    filterOptionValue: null,
    renderFilterIcon: () => {
        return h(NIcon, null, { default: () => h(IconSearch) })
    },
    renderFilterMenu: ({ hide }: { hide: any }) => {
        return h(
            NSpace,
            { style: { padding: '6px' }, vertical: true },
            {
                default: () => [
                    h(NInput, {
                        value: DEFAULT_COLUMNS[0].filterOptionValue,
                        onInput: (value: string) => {
                            DEFAULT_COLUMNS[0].filterOptionValue = value.toString();
                        },
                        placeholder: 'Enter text to filter'
                    }),
                    h(NGrid, {
                        xGap: 80,
                        cols: 2
                    }, {
                        default: () => [h(NGi, {
                        }, {
                            default: () => h(NButton, {
                                size: "small",
                                onClick: (e) => {
                                    // doClean(param)
                                    DEFAULT_COLUMNS[0].filterOptionValue = null;
                                    hide();
                                }
                            }, {
                                default: () => "清除"
                            })
                        }), h(NGi, {

                        }, {
                            default: () => h(NButton, {
                                size: "small",
                                type: "primary",
                                onClick: (e) => {
                                    console.log(`output->e`, DEFAULT_COLUMNS[0])
                                    hide();
                                }
                            }, {
                                default: () => "查询"
                            })
                        })]
                    }),
                ]
            }
        )
    },
    isActivated: true,
}, {
    title: '项目定义',
    key: 'projectDefinition',
    width: 200,
    isActivated: true,
}, {
    title: '中标合同价（元）',
    key: 'bidContractPrice',
    width: 180,
    render: (_: any, index: any) => {
        return formatNumber(_.bidContractPrice)
    },
    isActivated: true,
}, {
    title: '合同签订日期',
    key: 'contractSigningDate',
    width: 140,
    isActivated: true,
}, {
    title: '承包类型',
    key: 'contractType',
    width: 120,
    isActivated: true,
}, {
    title: '单体/群体',
    key: 'singleOrGroup',
    width: 140,
    render: (_: any, index: any) => {
        return h(NTag, {
            bordered: false,
            type: _.singleOrGroup === 0 ? 'warning' : 'success'
        }, {
            default: () => _.singleOrGroup === 0 ? '单体' : _.singleOrGroup === 1 ? '群体' : undefined
        })
    },
    isActivated: true,
}, {
    title: '操作',
    width: 140,
    isActivated: true,
},];
const formatNumber = (num: any): string => {

    if (num === undefined || num === null) {
        return '';
    }
    try {
        const numString = num.toString();
        if (numString.includes('*')) {
            return numString;
        } else {
            return new Intl.NumberFormat('en-US').format(numString);
        }
    } catch (err) {
        console.log(`output->err`, err)
        return '';
    }
};
type FyjsProjectManagementTable = {
    content?: FyjsProjectManagement[],
    totalElements?: number,
    totalPages?: number
}

type ItemStatus = {
    itemNum?: number,
    groupName?: string,
    color?: string
}

type InternalValue = {
    text?: string,
    datePicker?: [string, string],
    numberPicker?: [string, string],
}
export type {
    FyjsProjectManagement, MainProject, QueryParam, MyOption,
    FyjsProjectRelatedUnitsDTO, FyjsProjectManagementPersonnelDTO,
    FyjsProjectRecordedPersonnelDTO, FyjsProjectQualityTargetDTO, FyjsProjectItemDTO,
    ProjectDetailsDTO, ProjectDetailDTO, FyjsProjectManagementTable, ItemStatus, 
    ExtendedDataTableBaseColumn, InternalValue, SETTING
}

export {
    DEFAULT_COLUMNS, createColumnWithInputFilter, createColumnWithTemplate, 
    createColumnWithFilterOptions, createColumnWithDatePickerTemplate, optionsMap, DEFAULT_SETTING
}