// affiliated_company
const affiliatedCompanyData = [
    {
        text: '建设本部',
        label: '建设本部',
        value: '建设本部'
    },
    {
        text: '杭州分公司',
        label: '杭州分公司',
        value: '杭州分公司'
    }
];

// taxationMethod
const taxationMethodData = [
    {
        text: '一般计税',
        label: '一般计税',
        value: '一般计税'
    },
    {
        text: '简易计税',
        label: '简易计税',
        value: '简易计税'
    }
];

// projectStatus
const projectStatusData = [
    {
        text: '未开工',
        label: '未开工',
        value: '未开工'
    },
    {
        text: '在建',
        label: '在建',
        value: '在建'
    },
    {
        text: '停工',
        label: '停工',
        value: '停工'
    },
    {
        text: '竣工',
        label: '竣工',
        value: '竣工'
    },
    {
        text: '解除',
        label: '解除',
        value: '解除'
    }
];

const projectProgressData = [
    {
        text: '地基基础',
        label: '地基基础',
        value: '地基基础'
    },
    {
        text: '主体',
        label: '主体',
        value: '主体'
    },
    {
        text: '装饰装修',
        label: '装饰装修',
        value: '装饰装修'
    },
    {
        text: '室外配套',
        label: '室外配套',
        value: '室外配套'
    },
    {
        text: '完工',
        label: '完工',
        value: '完工'
    }
];

// 竣工结算状态
const completionSettlementStatusData = [
    {
        text: '审定',
        label: '审定',
        value: '审定'
    },
    {
        text: '在审',
        label: '在审',
        value: '在审'
    },
    {
        text: '装饰装修',
        label: '装饰装修',
        value: '装饰装修'
    },
    {
        text: '末送审',
        label: '末送审',
        value: '末送审'
    }
];

// projectSource 项目来源
const projectSourceData = [
    {
        text: '公开招标',
        label: '公开招标',
        value: '公开招标'
    },
    {
        text: '邀请招标',
        label: '邀请招标',
        value: '邀请招标'
    },
    {
        text: '直接发包',
        label: '直接发包',
        value: '直接发包'
    }
];

// performanceGuaranteeMethod 履约保证方式
const performanceGuaranteeMethodData = [
    {
        text: '现金',
        label: '现金',
        value: '现金'
    },
    {
        text: '银行保函',
        label: '银行保函',
        value: '银行保函'
    },
    {
        text: '担保公司担保',
        label: '担保公司担保',
        value: '担保公司担保'
    },
    {
        text: '其他',
        label: '其他',
        value: '其他'
    }
];

// 管控等级
const controlLevelData = [
    {
        text: '一般管控项目',
        label: '一般管控项目',
        value: '一般管控项目'
    },
    {
        text: '风险管理项目',
        label: '风险管理项目',
        value: '风险管理项目'
    }
];

// contractType 承包类型
const contractTypeData = [
    {
        text: '工程总承包',
        label: '工程总承包',
        value: '工程总承包'
    },
    {
        text: '施工总承包',
        label: '施工总承包',
        value: '施工总承包'
    },
    {
        text: '专业承包',
        label: '专业承包',
        value: '专业承包'
    },
    {
        text: '代建+',
        label: '代建+',
        value: '代建+'
    },
    {
        text: '劳务分包',
        label: '劳务分包',
        value: '劳务分包'
    },
    {
        text: '专业分包',
        label: '专业分包',
        value: '专业分包'
    },
    {
        text: '其他',
        label: '其他',
        value: '其他'
    }
];

// internalOperationalContractingModel 内部经营承包模式
const internalOperationalContractingModelData = [
    {
        text: '直营',
        label: '直营',
        value: '直营'
    },
    {
        text: '内部经营责任制',
        label: '内部经营责任制',
        value: '内部经营责任制'
    },
    {
        text: '联营',
        label: '联营',
        value: '联营'
    }
];

// natureOfPartyA 甲方性质
const natureOfPartyAData = [
    {
        text: '民企',
        label: '民企',
        value: '民企'
    },
    {
        text: '国企',
        label: '国企',
        value: '国企'
    },
    {
        text: '政府（事业单位）',
        label: '政府（事业单位）',
        value: '政府（事业单位）'
    },
    {
        text: '上市公司',
        label: '上市公司',
        value: '上市公司'
    },
    {
        text: '央企',
        label: '央企',
        value: '央企'
    },
    {
        text: '外资企业',
        label: '外资企业',
        value: '外资企业'
    }
];

// projectType 工程类型
const projectTypeData = [
    {
        text: '建筑',
        label: '建筑',
        value: '建筑'
    },
    {
        text: '市政',
        label: '市政',
        value: '市政'
    },
    {
        text: '水利',
        label: '水利',
        value: '水利'
    },
    {
        text: '桩基',
        label: '桩基',
        value: '桩基'
    },
    {
        text: '安装',
        label: '安装',
        value: '安装'
    },
    {
        text: '装修',
        label: '装修',
        value: '装修'
    }
];

// projectNature 工程性质
// 建筑
const projectNatureAData = [
    {
        text: '公共建筑',
        label: '公共建筑',
        value: '公共建筑'
    },
    {
        text: '住宅',
        label: '住宅',
        value: '住宅'
    },
    {
        text: '工业建筑',
        label: '工业建筑',
        value: '工业建筑'
    }
];

// 市政
const projectNatureBData = [
    {
        text: '道路桥梁',
        label: '道路桥梁',
        value: '道路桥梁'
    },
    {
        text: '园林绿化',
        label: '园林绿化',
        value: '园林绿化'
    },
    {
        text: '室外配套',
        label: '室外配套',
        value: '室外配套'
    },
    {
        text: '其他市政工程',
        label: '其他市政工程',
        value: '其他市政工程'
    }
];

// 水利
const projectNatureCData = [{
    text: '水利',
    label: '水利',
    value: '水利' 
}]

// 工程用途
// 住宅
const projectPurposeAData = [
    {
        text: '商品房',
        label: '商品房',
        value: '商品房'
    },
    {
        text: '商住楼',
        label: '商住楼',
        value: '商住楼'
    },
    {
        text: '安置房',
        label: '安置房',
        value: '安置房'
    },
    {
        text: '公寓',
        label: '公寓',
        value: '公寓'
    },
    {
        text: '小区改造',
        label: '小区改造',
        value: '小区改造'
    }
];

// 工业建筑
const projectPurposeBData = [
    {
        text: '厂房',
        label: '厂房',
        value: '厂房'
    },
    {
        text: '仓储（粮库、仓库、冷库）',
        label: '仓储（粮库、仓库、冷库）',
        value: '仓储（粮库、仓库、冷库）'
    }
];

// 公共建筑
const projectPurposeCData = [
    {
        text: '办公建筑',
        label: '办公建筑',
        value: '办公建筑'
    },
    {
        text: '酒店',
        label: '酒店',
        value: '酒店'
    },
    {
        text: '学校（科研）',
        label: '学校（科研）',
        value: '学校（科研）'
    },
    {
        text: '医疗（医养、医院）',
        label: '医疗（医养、医院）',
        value: '医疗（医养、医院）'
    },
    {
        text: '商业（商城、市场）',
        label: '商业（商城、市场）',
        value: '商业（商城、市场）'
    },
    {
        text: '其他公共建筑',
        label: '其他公共建筑',
        value: '其他公共建筑'
    }
];

const projectPurposeDData = [
    {
        text: '水利设施',
        label: '水利设施',
        value: '水利设施'
    },
    {
        text: '基础设施',
        label: '基础设施',
        value: '基础设施'
    }
];

const projectPurposeEData = [
    {
        text: '水厂',
        label: '水厂',
        value: '水厂'
    },
    {
        text: '管网',
        label: '管网',
        value: '管网'
    },
    {
        text: '美丽城镇',
        label: '美丽城镇',
        value: '美丽城镇'
    },
    {
        text: '公园',
        label: '公园',
        value: '公园'
    },
    {
        text: '环境提升',
        label: '环境提升',
        value: '环境提升'
    },
    {
        text: '土地平整',
        label: '土地平整',
        value: '土地平整'
    }
];

// structuralType 结构类型
const structuralTypeData = [
    {
        text: '混凝土框架结构',
        label: '混凝土框架结构',
        value: '混凝土框架结构'
    },
    {
        text: '混凝土框剪结构',
        label: '混凝土框剪结构',
        value: '混凝土框剪结构'
    },
    {
        text: '混凝土框筒结构',
        label: '混凝土框筒结构',
        value: '混凝土框筒结构'
    },
    {
        text: '砖混结构',
        label: '砖混结构',
        value: '砖混结构'
    },
    {
        text: '钢结构',
        label: '钢结构',
        value: '钢结构'
    },
    {
        text: '木结构',
        label: '木结构',
        value: '木结构'
    },
    {
        text: '沥青混凝土路面',
        label: '沥青混凝土路面',
        value: '沥青混凝土路面'
    },
    {
        text: '水泥混凝土路面',
        label: '水泥混凝土路面',
        value: '水泥混凝土路面'
    }
];


export {
    affiliatedCompanyData, taxationMethodData, projectProgressData, projectStatusData,
    completionSettlementStatusData, projectSourceData, performanceGuaranteeMethodData,
    controlLevelData, contractTypeData, internalOperationalContractingModelData,
    natureOfPartyAData, projectNatureAData, projectNatureBData, projectNatureCData, projectTypeData, 
    projectPurposeAData, projectPurposeBData, projectPurposeCData, projectPurposeDData, projectPurposeEData,  
    structuralTypeData
}