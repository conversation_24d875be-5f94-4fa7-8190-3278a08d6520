import { ref, Ref } from 'vue';
import { doPostAsync } from '../../../../utils/AxiosUtil'; // 确保路径正确
import type { MyData as Staff } from '../../../staff/index';
import { MyOption, QueryParam } from './index';

// 创建状态管理成员列表和选项
const members: Ref<Staff[]> = ref([]);
const memberOptions: Ref<MyOption[]> = ref([]);

// 获取成员列表
async function getMemberList(queryParams: QueryParam, columnKey?: string, staffName?: string, currentPage?: number) {
    queryParams.staffName = staffName;
    queryParams.columnKey = columnKey;
    queryParams.currentPage = currentPage;

    const url = '/fyschedule/js/staff/filter';
    try {
        const data:any = await doPostAsync(url, queryParams);
        if (data.code === 0) {
            members.value = [...data.result.content]; // 更新而不是追加，确保数据是最新的
            memberOptions.value = data.result.content.map((staff: Staff) => ({
                label: staff.staffName,
                value: staff.staffName,
                data: staff
            }));
        }
    } catch (error) {
        console.error('Failed to fetch members:', error);
    }
}

// 搜索成员
function handleMemberSearch(value: string, queryParams: QueryParam) {
    queryParams.currentPage = 0;
    getMemberList(queryParams, 'staffName', value, 0);
}

// 选择成员更新数据
function handleMemberSelect(e: string, dataToUpdate: any[], index: number) {
    const result = memberOptions.value.find(item => item.label === e);
    if (result) {
        dataToUpdate[index].contactNumber = result.data.phoneNumber;
        dataToUpdate[index].personId = result.data.id;
        dataToUpdate[index].avatar = result.data.avatar
    }
}

export { members, memberOptions, getMemberList, handleMemberSearch, handleMemberSelect };
