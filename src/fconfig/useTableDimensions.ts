import { ref, onMounted, onBeforeUnmount } from 'vue'

export function useTableDimensions() {
    const tableStyle = ref({
        height: '500px',
        width: '800px'
    })
    const spaceStyle = ref({ margin: '4px', display: 'flex', justifyContent: 'right', width: '800px' });
    const spaceStyleWithoutJustify = ref({ margin: '4px', display: 'flex', width: '800px' });

    const cardStyle = ref({
        width: '800px'
    })

    const width = ref(800);
    const maxHeight = ref(500);

    function updateTableDimensions() {
        maxHeight.value = window.innerHeight - 78;
        width.value = window.innerWidth - 20;
        tableStyle.value = {
            height: `${window.innerHeight - 78}px`,
            width: `${window.innerWidth - 20}px`
        }
        spaceStyle.value.width = `${window.innerWidth - 24}px`;
        spaceStyleWithoutJustify.value.width = `${window.innerWidth - 24}px`;
        cardStyle.value.width = `${window.innerWidth - 20}px`;
    }

    onMounted(() => {
        updateTableDimensions()
        window.addEventListener('resize', updateTableDimensions)
    })

    onBeforeUnmount(() => {
        window.removeEventListener('resize', updateTableDimensions)
    })

    return {
        tableStyle, maxHeight, width, spaceStyle, spaceStyleWithoutJustify, cardStyle,
        updateTableDimensions
    }
}
