<template>
    <div :style="{
        display: 'flex',
        width: '100%',
        boxSizing: 'border-box',
        padding: '0px',
        backgroundColor: 'var(--color-fill-2)',
    }">
        <n-card title="编辑对方户名" size="small" :style="cardStyle">
            <div class="custom-display">
                <counterparty-account ref="sc" :width="myWindow.width" />
            </div>
            <div class="fixed-bottom">
                <n-space style="width: 200px;">
                    <n-button type="primary" size="small" @click="handleSubmit">提交</n-button>
                    <n-button type="info" size="small" @click="goBack">返回</n-button>
                </n-space>
            </div>
        </n-card>
    </div>
</template>
<script lang="ts" setup>

    import CounterpartyAccount from '../../../components/fyg/accounting/counterparty/AccountEdit.vue'
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    const sc = ref<any>(null);

    const handleSubmit = (e: MouseEvent) => {
        console.log('handleSubmit called'); // Debug log
        sc.value.doSave(e)
    };
    //window size
    const Window: any = window
    type MyWindow = {
        width: number,
        height: number
    }
    const myWindow = reactive<MyWindow>({
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
    })

    // card 样式
    type CardStyle = {
        width: string;
        height: string;
    };
    const cardStyle = reactive<CardStyle>({
        width: myWindow.width - 20 + 'px',
        height: myWindow.height - 120 + 'px'
    });

    watch(myWindow, (newValue, oldValue) => {
        cardStyle.width = newValue.width - 20 + 'px';
        cardStyle.height = newValue.height - 120 + 'px';
    })

    onMounted(() => {
        Window.onresize = () => {
            return (() => {
                Window.fullHeight = document.documentElement.clientHeight
                Window.fullWidth = document.documentElement.clientWidth
                myWindow.height = Window.fullHeight
                myWindow.width = Window.fullWidth
            })()
        }
    })
</script>
<style scoped>
    .n-card {
        width: 100%;
    }

    .custom-display {
        display: flex;
        justify-content: center;
    }

    .fixed-bottom {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 20px;
        margin: auto;
        width: 100px;
        /* 你可以根据需要设置宽度 */
        text-align: center;
        /* background-color: #f8f9fa; */
        background-color: transparent;
        /* 背景颜色 */
        padding: 4px;
        border-radius: 10px;
        box-shadow: 0 0 0px rgba(0, 0, 0, 0.2);
    }
</style>