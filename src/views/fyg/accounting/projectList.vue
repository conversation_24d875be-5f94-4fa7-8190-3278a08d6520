<template>
    <n-space :style="spaceStyleWithoutJustify" justify="space-between">
        <n-button-group type="primary">
            <n-popover style="padding: 0 0 0 0;" position="br" trigger="click">
                <template #trigger>
                    <n-button class="button">字段配置</n-button>
                </template>

                <template #default>
                    <column-drag :app-remark="'方远集团财务管理用户配置'" :id="settingId" :data="userSetting" :user-id="userId"
                        :app-key="appKey" :app-router="appRouter" @update:column="handleColumnUpdate" />
                </template>
            </n-popover>
            <n-button class="button" v-if="hasAdminPermission"
                @click="navigateToTargetPage('/fyg/accounting/summary')">日报大表</n-button>
        </n-button-group>
        <n-button class="button" @click="navigateToTargetPage('/fyg/accounting/project/edit/new')">新增</n-button>
    </n-space>
    <n-data-table ref="ftable" remote :columns="pageData.columns.column" :data="pageData.data.content"
        :style="tableStyle" :loading="loading" :pagination="paginationReactive" :bordered="false" :single-line="false"
        @update-filters="combinedHandler" :max-height="tableMaxHeight" :scroll-x="tableWidth" />
</template>
<script setup lang="ts">
    import { fetchDataAlt, deserializeColumns, getColorObj } from '../../../utils/APIUtils';
    import { doPutAsync } from '../../../utils/AxiosUtil';
    import { getPermission as isAdmin } from '../../../utils/SysUtil';
    import { createColumnWithFilterOptions, createColumnWithTemplate, createColumnWithDatePickerTemplate, optionsMap, DEFAULT_SETTING } from '../../../components/fyg/accounting/projects/index';
    import { doPostAsync } from '../../../utils/AxiosUtil';
    import type { QueryParam, TableData, ExtendedDataTableBaseColumn, SETTING, Project } from '../../../components/fyg/accounting/projects/index'
    import { h, } from 'vue'
    import { NButton, NDropdown, NSpace } from 'naive-ui'
    import {
        DataTableBaseColumn,
        DataTableFilterState,
        DropdownOption
    } from 'naive-ui'
    import myTag from '../../../components/MyTagComponent.vue'
    import columnDrag from '../../../components/dragger/draggableSetting.vue'
    import { useRouter, onBeforeRouteLeave } from 'vue-router';
    import VueCookies from 'vue-cookies';
    const vueCookies: any = VueCookies
    import { inject } from 'vue';
    import { encryptRowId } from '../../../utils/APIUtils';
    // 从全局注入中获取 message 实例
    const message: any = inject('message');
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, width, spaceStyleWithoutJustify } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })
    const tableWidth = ref(0);
    watch(() => width.value, (newValue) => {
        const totalWidth = calculateTotalWidth();
        tableWidth.value = totalWidth;
    })

    const router = useRouter();
    const navigateToTargetPage = (path: string) => {
        router.push(path);
    }

    // 保存分页状态
    onBeforeRouteLeave((to, from, next) => {
        localStorage.setItem('projectListState', JSON.stringify({
            page: paginationReactive.page,
            pageSize: paginationReactive.pageSize,
            filters: { ...queryParam }
        }))
        next()
    })

    // 恢复状态的函数
    const restoreState = () => {
        const savedState = localStorage.getItem('projectListState')
        if (savedState) {
            const state = JSON.parse(savedState)
            paginationReactive.page = state.page
            paginationReactive.pageSize = state.pageSize
            Object.assign(queryParam, state.filters)
        }
    }


    const BASE_SERVER = '/fyschedule/fyg/accounting/project/';

    //table 
    const ftable = ref<any>(null);
    const loadingRef = ref(true)
    const loading = loadingRef
    const columns = reactive<any>({
        column: []
    })
    const data = reactive<TableData>({});

    const initialPaginationState = {
        itemCount: 0,
        pageCount: 1,
        page: 1,
        pageSize: 15,
        showSizePicker: true,
        pageSizes: [15, 25, 50],
        onChange: (page: number) => {
            paginationReactive.page = page;
            handleSearch();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize
            paginationReactive.page = 1
            handleSearch();
        },
        prefix({ itemCount }: { itemCount?: number }) {
            return `Total is ${itemCount}.`
        }
    }

    const paginationReactive = reactive({ ...initialPaginationState });

    // 定义重置方法
    async function resetPagination() {
        Object.assign(paginationReactive, { ...initialPaginationState });
    }

    const pageData = {
        data: data,
        columns: columns,
        pagination: paginationReactive
    }

    //query
    const initialQueryParam: QueryParam = {
        dataStatus: 1,
        currentPage: paginationReactive.page - 1,
        pageSize: paginationReactive.pageSize,
        projectName: undefined,
        columnKey: 'projectName',
        order: 'descend',
    };

    const queryParam = reactive<QueryParam>(initialQueryParam);

    async function resetQueryParam() {
        Object.assign(queryParam, { ...initialQueryParam });
    }

    const hasAdminPermission = ref<boolean>(false);
    //permission
    async function handlePermission() {
        isAdmin({ edit: 'fyg_accounting_administrator', read: 'fyg_accounting_accountant' }).then((res: any) => {
            if (res.scope === 'edit') {
                hasAdminPermission.value = res.data;
            } else if (res.scope === 'read') {
                queryParam.accountant = userId;

            } else {
                message.error('您没有权限访问该页面');
                loading.value = false;
            }
            handleSearch();
        })
    }


    //setting
    const userId = vueCookies.get('account') ? vueCookies.get('account') : undefined;

    const appKey = 'fyg_projects' //自定义当前项目的key
    const appRouter = 'index' //当前页面的route
    const settingId = undefined

    // 更新获取 userSetting 的函数
    const getUserSetting = async () => {
        const url = '/fyschedule/app/user/setting/find'
        const params = {
            userId: userId,
            appKey: appKey,
            appRouter: appRouter
        }
        try {
            const data: any = await doPostAsync(url, params);
            if (data.code === 0) {
                if (data.result?.appSetting) {
                    // 反序列化
                    const deserializedColumns = deserializeColumns(data.result.appSetting);
                    // 更新 userSetting
                    Object.assign(userSetting, deserializedColumns);
                    const { columnsArray, columnsMap } = setColumn(userSetting);
                    Object.assign(myColumnMap, columnsMap);
                    Object.assign(columns, { column: columnsArray });
                }
            } else {
                console.log(`Failed to fetch user setting, using default columns instead`);
                Object.assign(userSetting, DEFAULT_SETTING);
                const { columnsArray, columnsMap } = setColumn(userSetting);
                Object.assign(myColumnMap, columnsMap);
                Object.assign(columns, { column: columnsArray });
            }
        } catch (error) {
            console.error('Failed to fetch user setting:', error);
        }
    }

    const getColumn = (items: any) => {
        return items.filter((item: any) => item.isActivated !== false);
    }

    type FilterData = {
        key: string,
        value: any
    }
    const doFilter = async (val: FilterData) => {
        if (Array.isArray(val.value)) {
            let raw = normalizeValues(val.value);
            queryParam[val.key] = JSON.stringify(raw);
        } else {
            queryParam[val.key] = val.value;
        }
        await handleSearch()
    }

    type Field = {
        key: string,
        data: ExtendedDataTableBaseColumn,
        type: string,
        toDo?: Function,
        optionKey?: keyof typeof optionsMap,
    }

    const fields: Field[] = [{
        key: 'projectName',
        data: { title: '项目名称', key: 'projectName', width: 200, isActivated: true },
        type: 'text',
        toDo: doFilter
    }, {
        key: 'projectType',
        data: {
            title: '项目类型', key: 'projectType', width: 120, render: (_: any, index: number) => {
                return _.projectType ? h(myTag, {
                    value: _.projectType,
                    colorObject: getColorObj(_.projectType),
                }) : undefined;
            }, isActivated: true
        },
        type: 'options',
        optionKey: 'projectType'
    }, {
        key: 'contractSigningDate',
        data: { title: '合同签订日期', key: 'contractSigningDate', width: 140, isActivated: true },
        type: 'datePicker',
        toDo: doFilter
    }, {
        key: 'company',
        data: { title: '单位', key: 'company', width: 200, isActivated: true },
        type: 'text'
    }, {
        key: 'projectLocation',
        data: { title: '项目位置', key: 'projectLocation', width: 200, isActivated: true },
        type: 'text'
    }, {
        key: 'landAcquisitionDate',
        data: { title: '拿地时间', key: 'landAcquisitionDate', width: 140, isActivated: true },
        type: 'datePicker'
    }, {
        key: 'openingDate',
        data: { title: '开盘时间', key: 'openingDate', width: 140, isActivated: true },
        type: 'datePicker'
    }, {
        key: 'completionPreparationDate',
        data: { title: '竣备时间', key: 'completionPreparationDate', width: 140, isActivated: true },
        type: 'datePicker'
    }, {
        key: 'handoverDate',
        data: { title: '交房时间', key: 'handoverDate', width: 140, isActivated: true },
        type: 'datePicker'
    }, {
        key: 'landTransferFee',
        data: { title: '土地出购金(合同金额)', key: 'landTransferFee', width: 190, isActivated: true },
        type: 'text'
    }, {
        key: 'landArea',
        data: { title: '土地面积', key: 'landArea', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'totalConstructionArea',
        data: { title: '总建筑面积', key: 'totalConstructionArea', width: 160, isActivated: true },
        type: 'text'
    }, {
        key: 'totalInvestment',
        data: { title: '总投', key: 'totalInvestment', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'fundsInvested',
        data: { title: '已投入资金', key: 'fundsInvested', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'projectLoanBalance',
        data: { title: '项目贷款余额', key: 'projectLoanBalance', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'sellableArea',
        data: { title: '可售面积', key: 'sellableArea', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'soldArea',
        data: { title: '已售面积', key: 'soldArea', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'estimatedTotalValue',
        data: { title: '预计总货值', key: 'estimatedTotalValue', width: 160, isActivated: true },
        type: 'text'
    }, {
        key: 'salesAmount',
        data: { title: '销售金额', key: 'salesAmount', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'buybackAmount',
        data: { title: '回购金额', key: 'buybackAmount', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'fundsRecovered',
        data: { title: '已回笼资金', key: 'fundsRecovered', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'projectProgress',
        data: { title: '工程进度', key: 'projectProgress', width: 140, isActivated: true },
        type: 'text',
        toDo: doFilter
    }, {
        key: 'recoverableFunds',
        data: { title: '可回笼资金', key: 'recoverableFunds', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'fangyuanShareRatio',
        data: { title: '方远实际股比', key: 'fangyuanShareRatio', width: 140, isActivated: true },
        type: 'text'
    }, {
        key: 'accountant',
        data: { title: '项目财务', key: 'accountant', width: 140, isActivated: true },
        type: 'text'
    }];

    // 计算所有列宽总和的函数
    function calculateTotalWidth() {
        return fields.reduce((total, field) => {
            if (field.data && typeof field.data.width === 'number') {
                return total + (field.data.width as number);
            }
            return total;
        }, 0);
    }

    // 定义操作列
    const actions = (): ExtendedDataTableBaseColumn => {
        return {
            title: '操作',
            key: 'actions',
            width: 240,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(row, index)
                        }, { default: () => ('编辑') }),
                        dropdown(row),
                        dropdownChart(row),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(row, index)
                        }, { default: () => '删除' })
                    ]
                })
            },
            fixed: 'right',
            isActivated: true
        }
    }

    const generateOptions = (rowId?: number, name?: string) => {
        if (!rowId) return [];
        const encryptId = encryptRowId(rowId);
        return [
            // { label: '对方户名类型', key: `/fyg/accounting/counterparty/types/${encryptId}` },
            { label: '股权分配', key: `/fyg/accounting/counterparty/equity/${encryptId}/${name}` },
            { label: '银行账户', key: `/fyg/accounting/bank/${encryptId}/${name}` },
            { label: '数据源', key: `/fyg/accounting/datasource/list/${encryptId}/${name}` },
        ]
    };
    const handleSelect = (key: string, option: DropdownOption) => {
        router.push(key);
        // message.info(String(key))
    }
    const dropdown = (row: Project) => h(NDropdown, {
        placement: 'bottom-start',
        trigger: 'click',
        size: 'small',
        options: generateOptions(row.id, row.projectName),
        onSelect: handleSelect
    }, {
        default: () => h(NButton, {
            size: 'small',
        }, { default: () => ('管理') })
    });

    const generateOptionsChart = (rowId?: number, name?: string) => {
        if (!rowId) return [];
        const encryptId = encryptRowId(rowId);
        return [
            { label: '资金日报', key: `/fyg/accounting/daily/${encryptId}/${name}` },
            // { label: '现金流量表', key: `/fyg/accounting/cashflow/${encryptId}/${name}` },
        ]
    };

    const dropdownChart = (row: Project) => h(NDropdown, {
        placement: 'bottom-start',
        trigger: 'click',
        size: 'small',
        options: generateOptionsChart(row.id, row.projectName),
        onSelect: handleSelect
    }, {
        default: () => h(NButton, {
            size: 'small',
        }, { default: () => ('报表') })
    });



    // 定义 setColumn 函数，根据 userSetting 动态生成 columns
    const setColumn = (settings: SETTING[]) => {
        let columnsArray: ExtendedDataTableBaseColumn[] = [];
        let columnsMap: any = {};

        settings.forEach((setting) => {
            if (setting.isActivated) {
                let column: any;
                const field = fields.find(f => f.key === setting.key);
                if (field) {
                    if (field.type === 'text') {
                        column = createColumnWithTemplate(field.data, field.type, field.toDo);
                    } else if (field.type === 'options') {
                        if (field.optionKey)
                            column = createColumnWithFilterOptions(field.data, field.optionKey);
                    } else if (field.type === 'datePicker') {
                        column = createColumnWithDatePickerTemplate(field.data, field.type, field.toDo);
                    }
                    // 添加到数组
                    columnsArray.push(column);
                    // 同时添加到映射
                    columnsMap[field.data.key] = column;
                }
            }
        });
        columnsArray.push(actions());
        return { columnsArray, columnsMap };
    }

    // 获取列数据
    const userSetting = reactive<SETTING[]>(DEFAULT_SETTING);

    const { columnsArray, columnsMap } = setColumn(userSetting);
    const myColumnMap = reactive<any>({});
    const handleColumnUpdate = (e: any) => {
        if (e) {
            const { columnsArray, columnsMap } = setColumn(userSetting);
            Object.assign(myColumnMap, columnsMap);
            Object.assign(columns, { column: columnsArray });
        }
    }

    //filter
    const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        const key = initiatorColumn?.key;
        if (key && filters[key]) {
            // Ensure the values are treated as an array of strings
            const filterValues = filters[key];
            let filteredValues: string[] = [];

            if (Array.isArray(filterValues)) {
                // Assuming FilterOptionValue can be directly used as string
                filteredValues = filterValues.map(value => String(value));
            } else if (filterValues != null) {
                // Handle a single FilterOptionValue or non-array
                filteredValues = [String(filterValues)];
            }

            // Now filteredValues is guaranteed to be string[], can use it safely
            if (filteredValues.length > 0) {
                let raw = normalizeValues(filteredValues);
                queryParam[key + 'JsonStr'] = JSON.stringify(raw);
                handleSearch();
            } else {
                delete queryParam[key + 'JsonStr'];
                handleSearch();
            }
            myColumnMap[key].filterOptionValues = filters[key]

        }
    }




    function normalizeValues(values: string[]): (number | string | null)[] {
        return values.map(value => {
            // 检查是否为数字类型的字符串，如果是，则转换为数字
            if (/^\d+$/.test(value)) {
                return Number(value);
            } else {
                //不是数字的直接返回就好了
                return value;
            }
        }).filter(item => item !== null);
    }


    const anotherHandler = (filters: any) => {
        console.log('Second handler:', filters);
        // 其他处理逻辑
    };

    // 合并这两个处理器到一个事件监听器
    const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        handleFilters(filters, initiatorColumn);
        anotherHandler(filters);
    };



    function toggleEdit(row: Project, index: number) {
        if (row.id) {
            const encryptId = encryptRowId(row.id);
            router.push({
                path: '/fyg/accounting/project/edit/' + encryptId,
            });
        } else {
            message.error('项目ID无效，请刷新页面重试！');
        }
    }

    // 删除一行
    async function deleteRow(row: Project, index: number) {
        if (Array.isArray(data.content)) {
            // data.content 是一个数组，可以安全地使用 splice 方法
            const result = await deleteById(row.id);
            if (result) {
                data.content.splice(index, 1);
                message.success('删除成功！');
            }
        } else {
            // data.content 不是一个数组，抛出错误或进行其他处理
            console.error('data.content is not an array!');
        }
    }

    async function deleteById(id?: number) {
        if (!id) {
            message.error('项目ID无效，请刷新页面重试！');
            return false;
        }

        // 弹出确认对话框
        if (!window.confirm('您确定要删除这个项目吗？')) {
            // 如果用户点击了取消，则不执行删除操作
            return false;
        }

        const url = BASE_SERVER + 'update-status';
        const requestParams = {
            id,
            dataStatus: 0,
            updatedBy: userId
        };

        try {
            const response: any = await doPutAsync(url, requestParams);
            if (response.code === 200) {
                return true;
            } else {
                message.error(response.msg);
                return false;
            }
        } catch (error) {
            message.error('删除过程中发生错误，请稍后再试！');
            console.error(error);
            return false;
        }
    }

    //data
    async function getData(queryParam?: QueryParam) {
        const url = BASE_SERVER + 'filter'
        const results = await fetchDataAlt(url, queryParam);
        if (results.length > 0) {
            const result = results[0];
            result.content.forEach((item: Project) => {
                Object.keys(item).forEach(key => {
                    // 仅当项的值不是 null 或 undefined 时才继续处理
                    if (item[key] != null && typeof item[key] === 'string') {
                        const trimmedValue = item[key].trim();
                        // 如果字符串不为空，尝试转换为数字
                        if (trimmedValue !== '') {
                            const convertedNumber = Number(trimmedValue);
                            // 只有当转换结果为有效数字时，才更新该值
                            if (!isNaN(convertedNumber)) {
                                item[key] = convertedNumber;
                            }
                        }
                    }
                });
            });

            Object.assign(data, {
                ...result  // 确保从服务器返回的结果是完整的，包括所有需要的字段
            });
        }
        // 获取测试项目数据
        // fetchProjects().then((result) => {
        //     console.log('fetchProjects response:', result); // Debug log
        //     data.content = result;
        // });
        paginationReactive.pageCount = data.totalPages ? data.totalPages : 0;
        paginationReactive.itemCount = data.totalElements ? data.totalElements : 0;
        setTimeout(() => {
            loadingRef.value = false
        }, 1000);
    }

    const handleSearch = async () => {
        loadingRef.value = true
        queryParam.pageSize = paginationReactive.pageSize;
        queryParam.currentPage = paginationReactive.page - 1;
        await getData(queryParam);
    }

    async function initData() {
        await getUserSetting();
        await handlePermission();

    }

    onMounted(() => {
        restoreState();
        initData();
    })

</script>
<style scoped></style>