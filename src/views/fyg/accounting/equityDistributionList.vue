<template>
    <n-space :style="spaceStyleWithoutJustify" justify="space-between">
        <n-h3>{{ name }}</n-h3>
        <n-button-group>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <n-data-table :columns="columns" :data="data" :single-line="false" :pagination="pagination"
        :max-height="tableMaxHeight" :style="tableStyle" :loading="loading" />
</template>

<script setup lang="ts">
    import { fetchDataAlt, deleteById } from '../../../utils/APIUtils';
    import { doPutAsync } from '../../../utils/AxiosUtil';
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    import { h, ref, watch } from 'vue'
    import { NButton, NInput, NSpace, NSelect, NInputNumber, NH3 } from 'naive-ui'
    import type { DataTableColumns } from 'naive-ui'
    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const editor = vueCookies.get('account');
    import { decryptRowId } from '../../../utils/APIUtils';
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const name: string = route.params?.name as string;
    const encryptedId: string = route.params?.id as string;
    const projectId: number = decryptRowId(encryptedId);
    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    const BASE_SERVER = '/fyschedule/fyg/accounting/equity-distribution/'
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyleWithoutJustify } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })
    const message = useMessage()

    const equityTypeOptions = [
        { label: '明股', value: 'MING_GU' },
        { label: '暗股', value: 'AN_GU' }
    ]

    const loading = ref(false)
    function createData(): EditableEquityDistributionType[] {
        return [
        ]
    }

    const data = ref(createData())

    interface EditableEquityDistributionType {
        id?: number
        projectId: number
        // counterpartyAccountId: number
        counterpartyAccountName?: string
        equityType: string
        equityRatio: number
        transferDate?: number
        dataStatus?: number
        createdAt?: number
        updatedAt?: number
        createdBy?: string
        updatedBy?: string
        editable?: boolean
        [key: string]: any;
    }

    const createColumns = (): DataTableColumns<EditableEquityDistributionType> => [
        // {
        //     title: '项目ID',
        //     key: 'projectId',
        //     render(row, index) {
        //         return row.editable
        //             ? h(NSelect, {
        //                 options: projects,
        //                 value: row.projectId,
        //                 onUpdateValue(v) {
        //                     data.value[index].projectId = Number(v)
        //                 }
        //             })
        //             : projects.find(project => project.value === row.projectId)?.label || row.projectId
        //     }
        // },
        // {
        //     title: '股东ID',
        //     key: 'counterpartyAccountId',
        //     render(row, index) {
        //         return row.editable
        //             ? h(NSelect, {
        //                 options: counterpartyAccounts,
        //                 value: row.counterpartyAccountId,
        //                 onUpdateValue(v) {
        //                     data.value[index].counterpartyAccountId = Number(v)
        //                 }
        //             })
        //             : counterpartyAccounts.find(account => account.value === row.counterpartyAccountId)?.label || row.counterpartyAccountId
        //     }
        // },
        {
            title: '股东名称',
            key: 'counterpartyAccountName',
            render(row, index) {
                return row.editable ? h(NInput, {
                    value: row.counterpartyAccountName,
                    onUpdateValue(v) {
                        data.value[index].counterpartyAccountName = v
                    }
                })
                    : row.counterpartyAccountName
            }
        },
        {
            title: '股权类型',
            key: 'equityType',
            render(row, index) {
                return row.editable
                    ? h(NSelect, {
                        options: equityTypeOptions,
                        value: row.equityType,
                        onUpdateValue(v) {
                            data.value[index].equityType = v
                        }
                    })
                    : equityTypeOptions.find(option => option.value === row.equityType)?.label;
            }
        },
        {
            title: '股权比例',
            key: 'equityRatio',
            render(row, index) {
                return row.editable
                    ? h(NInputNumber, {
                        value: row.equityRatio,
                        showButton: false,
                        onUpdateValue(v) {
                            data.value[index].equityRatio = Number(v)
                        }
                    }, {
                        suffix: () => '%'
                    })
                    : `${row.equityRatio}%`
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 180,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(row, index)
                        }, { default: () => '删除' })
                    ]
                })
            }
        }
    ]

    const columns = createColumns()

    const pagination = {
        pageSize: 15
    }

    // 切换编辑状态
    function toggleEdit(index: number) {
        if (data.value[index].editable) {
            // 切换为编辑状态时，需要获取当前行数据
            saveData(index);
        }
        data.value[index].editable = !data.value[index].editable
    }


    async function saveData(index: number) {
        loading.value = true
        const url = BASE_SERVER + 'save';
        data.value[index].updatedBy = editor;
        const response: any = await doPutAsync(url, data.value[index]);
        if (response.code === 200) {
            data.value[index] = { ...response.data }
            message.success(response.message)
        } else {
            message.error(`${response.message} 错误代码: ${response.code}`)
        }
        loading.value = false
    }

    // 新增一行
    function addRow() {
        data.value.push({
            id: undefined, // 新增行的id
            projectId: projectId,
            counterpartyAccountName: '',
            equityType: '',
            equityRatio: 0,
            dataStatus: 1,
            // createdAt: Date.now(),
            // updatedAt: Date.now(),
            createdBy: editor,
            editable: true // 新增行处于可编辑状态
        })
    }

    // 删除一行
    async function deleteRow(row: EditableEquityDistributionType, index: number) {
        if (!row.id) data.value.splice(index, 1)
        else {
            const url = BASE_SERVER + 'update-status';
            const params = {
                id: row.id,
                dataStatus: 0,
                updatedBy: editor
            }
            const result = await deleteById(url, params);
            if (result) {
                message.success('删除成功');
                data.value.splice(index, 1);
            } else {
                message.error('删除失败');
            }
        }
    }

    //data
    async function getData() {
        if (!projectId) return
        loading.value = true
        const url = BASE_SERVER + 'list';
        const params = {
            dataStatus: 1,
            projectId: projectId
        }
        const results = await fetchDataAlt(url, params);
        if (results.length > 0) {
            data.value = results
        }
        loading.value = false
    }

    onMounted(() => {
        getData()
    })
</script>

<style scoped>
    /* 表格页面样式 */
</style>
