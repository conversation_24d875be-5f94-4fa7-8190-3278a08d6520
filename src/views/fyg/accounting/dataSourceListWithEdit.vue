<template>

    <n-space :style="spaceStyleWithoutJustify" justify="space-between">
        <n-h3>{{ name }}</n-h3>
        <n-button-group>
            <n-button @click="handlePasteFromClipboard">从剪贴板粘贴</n-button>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <!-- <n-data-table :columns="columns" :data="data" :single-line="false" :style="tableStyle" :max-height="tableMaxHeight"
        :scroll-x="tableWidth" @update-filters="combinedHandler" virtual-scroll @scroll="handleScroll"
        :loading="loading" /> -->
    <n-data-table remote :columns="columns" :data="data" :style="tableStyle" :loading="loading" :bordered="false"
        :single-line="false" @update-filters="combinedHandler" :virtual-scroll="!hasEditableRow" :row-key="getRowKey"
        @scroll="handleScroll" :max-height="tableMaxHeight" :scroll-x="tableWidth" />
</template>

<script setup lang="ts">
    import { v4 as uuidv4 } from 'uuid';
    import { doPutAsync } from '../../../utils/AxiosUtil';
    import MyDatePicker from '../../../components/MyDateFilterComponent.vue';
    import MyNumberPicker from '../../../components/MyNumberFilterComponent.vue';
    import MyFilter from '../../../components/MyFilterComponent.vue';
    import { normalizeValues } from '../../../utils/xUtils';
    import { fundDailyDimensionOptions as fundDailyFilterDimensionOptions, counterpartyTypeOptions } from '../../../components/fyg/accounting/dataSource';
    import { h, ref, VNodeChild, watch } from 'vue';
    import { NButton, NSpace, NInputNumber, NSelect, NDatePicker, NIcon, NInput, } from 'naive-ui';
    import { Search as IconSearch, } from '@vicons/ionicons5'
    import { SelectOption, SelectGroupOption } from 'naive-ui';
    import type { DataTableBaseColumn, DataTableColumns, AutoCompleteOption, DataTableFilterState } from 'naive-ui';
    import type { FygDataSource, QueryParam } from '../../../components/fyg/accounting/dataSource';
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    import { useMessage } from 'naive-ui'
    const message = useMessage()
    import { formatDate } from '../../../utils/xUtils';
    import { decryptRowId, deleteById, fetchDataAlt } from '../../../utils/APIUtils';
    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const editor = vueCookies.get('account');
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const name: string = route.params?.name as string;
    const encryptedId: string = route.params?.id as string;
    const projectId = decryptRowId(encryptedId);
    const BASE_SERVER = '/fyschedule/fyg/accounting/dataSource/'
    const goBack = () => {
        router.back(); // 返回上一级页面
    };

    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyleWithoutJustify, width } = useTableDimensions();
    const tableMaxHeight = ref(100);
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue;
    });
    const tableWidth = ref(2000);
    watch(() => width.value, (newValue) => {
        const total = calculateTotalColumnWidth(columns);
        tableWidth.value = total + 2000;
    })

    interface BankAccountType {
        id?: number
        bankName: string
        accountNumber: string
        abbreviation?: string
    }

    const banks = ref<BankAccountType[]>([
    ]);

    const getBanks = async () => {
        const url = '/fyschedule/fyg/accounting/bank-account/list';
        const result = await fetchDataAlt(url, { dataStatus: 1, projectId: projectId });
        banks.value = result;
    }

    // const counterpartyOptions = ref([
    //     { label: '方远房地产集团有限公司', value: 1 },
    //     { label: '台州市路桥区路南街道方林村经济合作社', value: 2 }
    // ]);

    // const couterpartyAutoCompleteOptions = computed<AutoCompleteOption[]>(() => {
    //     return counterpartyOptions.value.map((item) => ({
    //         label: item.label,
    //         value: String(item.value) // Convert value to string for compatibility
    //     }));
    // });

    const fundDailyDimensionOptions: Array<SelectOption | SelectGroupOption> = [
        {
            label: '空值',
            value: undefined,
        },
        {
            type: 'group',
            label: '收入',
            key: '收入',
            children: [
                { label: '销售收入', value: 1 },
                { label: '其他收入', value: 2 },
                { label: '资金流入-股东方', value: 3 },
                { label: '资金流入-内部单位', value: 4 },
                { label: '资金流入-外部单位', value: 5 },
                { label: '融资流入', value: 6 }
            ]
        },
        {
            type: 'group',
            label: '支出',
            key: '支出',
            children: [
                { label: '土地支出', value: 7 },
                { label: '工程支出', value: 8 },
                { label: '税金支出', value: 9 },
                { label: '销售费用', value: 10 },
                { label: '管理费用', value: 11 },
                { label: '财务费用', value: 12 },
                { label: '其他支出', value: 13 },
                { label: '资金流出-股东方', value: 14 },
                { label: '资金流出-内部单位', value: 15 },
                { label: '资金流出-外部单位', value: 16 },
                { label: '融资还贷支出', value: 17 },
                { label: '融资费用', value: 18 }
            ]
        }
    ];

    const length = ref<number>(50);
    async function handleScroll(e: Event) {
        if (hasEditableRow.value) {
            console.log('存在可编辑行，滚动加载数据暂停');
            return; // 如果有可编辑行，直接返回
        }
        const target = e.target as HTMLElement;

        // 判断是否是垂直方向滚动
        if (target.scrollTop === 0 && target.scrollHeight === target.clientHeight) {
            return; // 没有垂直滚动
        }

        // 定义一个容忍阈值
        const threshold = 1; // 允许 1 像素误差，避免因小数而误判

        // 计算是否已经滑动到底部
        const isBottom = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <= threshold;
        if (isBottom) {
            console.log('已滑动到底部');
            length.value += 10;
            // 在此处执行其他逻辑，例如加载更多数据
            if (pagination.page !== pagination.pageCount) {
                pagination.page += 1;
                await handleSearch(false);
            } else {
                console.log('已加载全部数据');
            }
        }
    }


    function createData(): EditableFygDataSource[] {
        return [
        ];

        // return Array.from({ length: length.value }).map((_, index) => ({
        //     id: index,
        //     transactionDate: new Date('2024-02-01').getTime(),
        //     bankAccountId: 1,
        //     incomeAmount: 100000,
        //     expenseAmount: 0,
        //     counterpartyAccountId: 1,
        //     counterpartyName: `方远房地产集团有限公司 ${index}`,
        //     fundDailyDimensionId: 1,
        //     taxDetail: '增值税',
        //     developmentCostDetail: '建安工程费',
        //     projectId: 101,
        //     purposeDetail: '工程款支付',
        //     counterpartyAccountNumber: '**********',
        //     counterpartyBankName: '中国银行椒江支行',
        //     counterpartyType: 'GU_DONG_FANG',
        //     annualInterestRate: 5.5,
        //     dataStatus: 1,
        //     createdAt: new Date('2024-02-01').getTime(),
        //     updatedAt: new Date('2024-02-02').getTime(),
        //     createdBy: 'Admin',
        //     updatedBy: 'Admin',
        //     editable: false
        // }))
    }

    const data = ref(createData());

    interface EditableFygDataSource extends FygDataSource {
        editable?: boolean;
        [key: string]: any;
    }
    //filter
    const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        const key = initiatorColumn?.key;
        if (key && filters[key]) {
            // Ensure the values are treated as an array of strings
            const filterValues = filters[key];
            let filteredValues: string[] = [];

            if (Array.isArray(filterValues)) {
                // Assuming FilterOptionValue can be directly used as string
                filteredValues = filterValues.map(value => String(value));
            } else if (filterValues != null) {
                // Handle a single FilterOptionValue or non-array
                filteredValues = [String(filterValues)];
            }

            // Now filteredValues is guaranteed to be string[], can use it safely
            if (filteredValues.length > 0) {
                let raw = normalizeValues(filteredValues);
                queryParam[key + 'JsonStr'] = JSON.stringify(raw);
                handleSearch(true);
            } else {
                delete queryParam[key + 'JsonStr'];
                handleSearch(true);
            }
            columnsMap[key].filterOptionValues = filters[key]
        }
    }

    const anotherHandler = (filters: any) => {
        console.log('Second handler:', filters);
        // 其他处理逻辑
    };

    // 合并这两个处理器到一个事件监听器
    const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        handleFilters(filters, initiatorColumn);
        anotherHandler(filters);
    };

    //table
    const loadingRef = ref(false);
    const loading = loadingRef;

    const initialPaginationState = {
        itemCount: 0,
        pageCount: 1,
        page: 1,
        pageSize: 15,
        showSizePicker: true,
        pageSizes: [15, 25, 50],
        onChange: (page: number) => {
            paginationReactive.page = page;
            handleSearch(false);
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize
            paginationReactive.page = 1
            handleSearch(false);
        },
        prefix({ itemCount }: { itemCount?: number }) {
            return `Total is ${itemCount}.`
        }
    }

    const paginationReactive = reactive({ ...initialPaginationState });

    //query
    const initialQueryParam: QueryParam = {
        dataStatus: 1,
        currentPage: paginationReactive.page - 1,
        pageSize: paginationReactive.pageSize,
        projectName: undefined,
        columnKey: 'transactionDate',
        projectId: projectId !== 0 ? projectId : undefined,
        order: 'descend'
    };

    const queryParam = reactive<QueryParam>({ ...initialQueryParam });

    type FilterData = {
        key: string,
        value: any
    }
    const doFilter = async (val: FilterData) => {
        if (Array.isArray(val.value)) {
            let raw = normalizeValues(val.value);
            queryParam[val.key] = JSON.stringify(raw);
        } else {
            queryParam[val.key] = val.value;
        }
        await handleSearch(true)
    }

    const handleSearch = async (mark: boolean) => {
        loadingRef.value = true
        queryParam.pageSize = paginationReactive.pageSize;
        queryParam.currentPage = paginationReactive.page - 1;
        if (mark) {
            data.value = [];
        }
        await getData(queryParam);
    }

    async function getData(queryParam: QueryParam) {
        const url = BASE_SERVER + 'filter';
        const results = await fetchDataAlt(url, queryParam);
        if (results.length > 0) {
            const result = results[0];
            // result.content.forEach((item: EditableFygDataSource) => {
            //     Object.keys(item).forEach(key => {
            //         // 仅当项的值不是 null 或 undefined 时才继续处理
            //         if (item[key] != null && typeof item[key] === 'string') {
            //             const trimmedValue = item[key].trim();
            //             // 如果字符串不为空，尝试转换为数字
            //             if (trimmedValue !== '') {
            //                 const convertedNumber = Number(trimmedValue);
            //                 // 只有当转换结果为有效数字时，才更新该值
            //                 if (!isNaN(convertedNumber)) {
            //                     item[key] = convertedNumber;
            //                 }
            //             }
            //         }
            //     });
            // });
            result.content.forEach((item: EditableFygDataSource) => {
                data.value.unshift(item);
            });
            paginationReactive.pageCount = result.totalPages ? result.totalPages : 0;
            paginationReactive.itemCount = result.totalElements ? result.totalElements : 0;
            // Object.assign(data, {
            //     ...result  // 确保从服务器返回的结果是完整的，包括所有需要的字段
            // });
        }
        setTimeout(() => {
            loadingRef.value = false
        }, 1000);
    }
    const transactionDateFilterColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
        title: '交易日期',
        key: 'transactionDate',
        width: 60,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }) => {
            return h(MyDatePicker, {
                hide, type: 'datePicker', filterOptionValue: transactionDateFilterColumn.filterOptionValue, onFilter: (val: any) => {
                    if (val) {
                        transactionDateFilterColumn.filterOptionValue = val;
                    } else {
                        transactionDateFilterColumn.filterOptionValue = null;
                    }
                    doFilter({ key: 'transactionDateJsonStr', value: val });
                }
            });
        },
        render(row, index) {
            return row.editable
                ? h(NDatePicker, {
                    value: row.transactionDate,
                    type: 'date',
                    onUpdateValue(v) {
                        data.value[index].transactionDate = new Date(v).getTime();
                    }
                })
                : formatDate(row.transactionDate, 'YYYY-MM-DD');
        }
    });

    // const bankAccountIdFilterColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
    //     title: '银行账户',
    //     key: 'bankAccountId',
    //     filter: true,
    //     filterMultiple: true,
    //     filterOptionValues: null,
    //     filterOptions: bankOptions,
    //     render(row, index) {
    //         return row.editable
    //             ? h(NSelect, {
    //                 options: bankOptions,
    //                 value: row.bankAccountId,
    //                 consistentMenuWidth: false,
    //                 onUpdateValue(v) {
    //                     data.value[index].bankAccountId = v;
    //                 }
    //             })
    //             : bankOptions.find(option => option.value === row.bankAccountId)?.label;
    //     }
    // },)


    const bankAccountNumberFilterColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
        title: '银行账号',
        key: 'bankAccountNumber',
        width: 80,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }) => {
            return h(MyFilter, {
                hide, type: 'text', filterOptionValue: bankAccountNumberFilterColumn.filterOptionValue, onFilter: (val: any) => {
                    bankAccountNumberFilterColumn.filterOptionValue = val;
                    doFilter({ key: 'bankAccountNumber', value: val });
                }
            });
        },
        render(row, index) {
            return row.editable
                ? h(NInput, {
                    value: row.bankAccountNumber,
                    onUpdateValue(v) {
                        data.value[index].bankAccountNumber = v;
                        const bank = banks.value.find(bank => bank.accountNumber === v);
                                    if (bank) {
                                        row.bankAbbreviation = bank.abbreviation;
                                        row.bankAccountName = bank.bankName;
                                        row.bankAccountId = bank.id;
                                    }
                    }
                })
                : row.bankAccountNumber;
        }
    });

    // const counterpartyNameColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
    //     title: '对方户名',
    //     key: 'counterpartyName',
    //     width: 200,
    //     filter: true,
    //     filterOptionValue: null,
    //     renderFilterIcon: () => {
    //         return h(NIcon, null, { default: () => h(IconSearch) });
    //     },
    //     renderFilterMenu: ({ hide }) => {
    //         return h(MyFilter, {
    //             hide, type: 'text', filterOptionValue: counterpartyNameColumn.filterOptionValue, onFilter: (val: any) => {
    //                 counterpartyNameColumn.filterOptionValue = val;

    //                 doFilter({ key: 'counterpartyName', value: val });
    //             }
    //         });
    //     },
    //     render(row, index) {
    //         return row.editable
    //             ? h(NAutoComplete, {
    //                 value: row.counterpartyName,
    //                 options: couterpartyAutoCompleteOptions.value,
    //                 onUpdateValue(v) {
    //                     data.value[index].counterpartyName = v;
    //                 },
    //                 onSelect(v) {
    //                     data.value[index].counterpartyAccountId = Number(v);
    //                 },
    //                 getShow(v) {
    //                     return true;
    //                 },
    //                 renderLabel: (option: SelectOption): VNodeChild => [
    //                     h(NTooltip, { effect: 'dark', placement: 'top', trigger: 'hover' }, {
    //                         default: () => option.label as string,
    //                         trigger: () => h('div', { style: 'width: 100%' }, option.label as string)
    //                     })
    //                 ],
    //             })
    //             : row.counterpartyName;
    //     }
    // });

    const counterpartyTypeFilterColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
        title: '对方类型',
        key: 'counterpartyType',
        width: 60,
        filter: true,
        filterMultiple: true,
        filterOptionValues: [],
        filterOptions: counterpartyTypeOptions,
        render(row, index) {
            return row.editable
                ? h(NSelect, {
                    options: counterpartyTypeOptions,
                    value: row.counterpartyType,
                    onUpdateValue(v) {
                        data.value[index].counterpartyType = v;
                    }
                }) : counterpartyTypeOptions.find(option => option.value === row.counterpartyType)?.label;
        }
    });

    const fundDailyDimensionIdColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
        title: '资金科目名称',
        key: 'fundDailyDimensionId',
        width: 80,
        filter: true,
        filterMultiple: true,
        filterOptionValues: [],
        filterOptions: fundDailyFilterDimensionOptions,
        render(row, index) {
            return row.editable
                ? h(NSelect, {
                    options: fundDailyDimensionOptions,
                    value: row.fundDailyDimensionId,
                    consistentMenuWidth: false,
                    onUpdateValue(v) {
                        data.value[index].fundDailyDimensionId = v;
                    }
                })
                : findLabelByValue(row.fundDailyDimensionId!);
        }
    });

    const annualInterestRateColumn: DataTableBaseColumn<EditableFygDataSource> = reactive({
        title: '年利率(%)',
        key: 'annualInterestRate',
        width: 60,
        render(row, index) {
            return row.editable
                ? h(NInputNumber, {
                    value: row.annualInterestRate,
                    showButton: false,
                    precision: 2,
                    onUpdateValue(v) {
                        data.value[index].annualInterestRate = Number(v);
                    }
                }, {
                    suffix: () => '%'
                })
                : row.annualInterestRate ? row.annualInterestRate!.toFixed(2) : undefined;
        }
    })


    const createNumberColumn = (title: string, key: string, width?: number, mark?: string): DataTableBaseColumn<EditableFygDataSource> => {
        const column: DataTableBaseColumn<EditableFygDataSource> = reactive({
            title,
            key,
            width: width ? width : 150,
            filter: true,
            filterOptionValue: null,
            renderFilterIcon: () => {
                return h(NIcon, null, { default: () => h(IconSearch) });
            },
            renderFilterMenu: ({ hide }) => {
                return h(MyNumberPicker, {
                    hide, type: 'numberPicker', filterOptionValue: column.filterOptionValue, onFilter: (val: any) => {
                        if (val) {
                            column.filterOptionValue = val;
                        } else {
                            column.filterOptionValue = null;
                        }
                        doFilter({ key: key + 'JsonStr', value: val });
                    }
                });
            },
            render(row, index) {
                return row.editable
                    ? h(NInputNumber, {
                        value: row[key],
                        showButton: false,
                        precision: 2,
                        onUpdateValue(v) {
                            data.value[index][key] = Number(v);
                        }
                    }, {
                        suffix: () => mark
                    })
                    : row[key]!.toFixed(2);
            }
        });
        return column;
    };

    const incomeAmountFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createNumberColumn('收入金额(元)', 'incomeAmount', 80, '元');

    const expenseAmountFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createNumberColumn('支出金额(元)', 'expenseAmount', 80,
        '元');
    const createInputColumn = (title: string, key: string, width?: number): DataTableBaseColumn<EditableFygDataSource> => {
        const column: DataTableBaseColumn<EditableFygDataSource> = reactive({
            title,
            key,
            width: width ? width : 150,
            filter: true,
            filterOptionValue: null,
            renderFilterIcon: () => {
                return h(NIcon, null, { default: () => h(IconSearch) });
            },
            renderFilterMenu: ({ hide }) => {
                return h(MyFilter, {
                    hide, type: 'text', filterOptionValue: column.filterOptionValue, onFilter: (val: any) => {
                        column.filterOptionValue = val;
                        doFilter({ key, value: val });
                    }
                });
            },
            render(row, index) {
                return row.editable ? h(NInput, {
                    value: row[key],
                    onUpdateValue(v) {
                        data.value[index][key] = v;
                    }
                }) : row[key];
            }
        });
        return column;
    }

    const counterpartyNameColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('对方户名', 'counterpartyName', 80);

    const counterpartyAccountNumberFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('对方账号', 'counterpartyAccountNumber', 80);

    const bankAbbreviationFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('银行账户简称', 'bankAbbreviation', 80);

    const bankAccountNameFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('银行账户', 'bankAccountName', 80);

    const purposeDetailFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('资金用途', 'purposeDetail', 80);

    const secondaryDetailFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('收支二级明细', 'secondaryDetail', 80);

    const tertiaryDetailFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('收支三级明细', 'tertiaryDetail', 80);

    const remarksFilterColumn: DataTableBaseColumn<EditableFygDataSource> = createInputColumn('备注', 'remark', 80);

    const columnsMap: any = {
        transactionDate: transactionDateFilterColumn,
        bankAccountName: bankAccountNameFilterColumn,
        bankAccountNumber: counterpartyAccountNumberFilterColumn,
        bankAbbreviation: bankAbbreviationFilterColumn,
        incomeAmount: incomeAmountFilterColumn,
        expenseAmount: expenseAmountFilterColumn,
        // bankAccountId: bankAccountIdFilterColumn,
        counterpartyName: counterpartyNameColumn,
        counterpartyAccountNumber: counterpartyAccountNumberFilterColumn,
        fundDailyDimensionId: fundDailyDimensionIdColumn,
        annualInterestRate: annualInterestRateColumn,
        counterpartyType: counterpartyTypeFilterColumn,
        purposeDetail: purposeDetailFilterColumn,
        secondaryDetail: secondaryDetailFilterColumn,
        tertiaryDetail: tertiaryDetailFilterColumn,
        remark: remarksFilterColumn
    }

    const createColumns = (): DataTableColumns<EditableFygDataSource> => reactive([
        transactionDateFilterColumn,
        // bankAccountIdFilterColumn,
        bankAccountNameFilterColumn,
        bankAccountNumberFilterColumn,
        bankAbbreviationFilterColumn,
        incomeAmountFilterColumn,
        expenseAmountFilterColumn,
        fundDailyDimensionIdColumn,
        counterpartyNameColumn,
        counterpartyAccountNumberFilterColumn,
        counterpartyTypeFilterColumn,
        annualInterestRateColumn,
        purposeDetailFilterColumn,
        secondaryDetailFilterColumn,
        tertiaryDetailFilterColumn,
        remarksFilterColumn,
        {
            title: '操作',
            key: 'action',
            width: 54,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(row, index)
                        }, { default: () => '删除' })
                    ]
                });
            },
            fixed: 'right',
        }
    ]);

    function calculateTotalColumnWidth(columns: DataTableColumns<EditableFygDataSource>) {
        return columns.reduce((totalWidth, column: EditableFygDataSource) => {
            // 如果列的宽度未设置，则使用默认宽度 150
            const columnWidth = typeof column.width === 'number' ? column.width : 150;
            return totalWidth + columnWidth;
        }, 0);
    }
    const columns: DataTableColumns<EditableFygDataSource> = createColumns();
    const pagination = paginationReactive;

    // 切换编辑状态
    const hasEditableRow = ref(false);
    function toggleEdit(index: number) {
        if (data.value[index].editable) {
            // 切换为编辑状态时，需要获取当前行数据
            saveData(index);
        }
        data.value[index].editable = !data.value[index].editable;
        // 检查是否还有可编辑行
        hasEditableRow.value = data.value.some(row => row.editable);
    }

    async function saveData(index: number) {
        loading.value = true;
        const url = BASE_SERVER + 'save';
        data.value[index].updatedBy = editor;
        const response: any = await doPutAsync(url, data.value[index]);
        if (response.code === 200) {
            data.value[index] = { ...response.data }
            message.success(response.message)
        } else {
            message.error(`${response.message} 错误代码: ${response.code}`)
        }
        loading.value = false;
    }

    // 为每行设置唯一的 row-key
    function getRowKey(row: EditableFygDataSource): string | number {
        return row.id ? row.id : row.tempId ? row.tempId : uuidv4(); // 保证至少返回 tempId 或 id，不会返回 undefined
    }


    // 新增一行
    function addRow() {
        data.value.unshift({
            id: undefined,
            tempId: uuidv4(),  // 使用 UUID 生成唯一的临时 ID
            transactionDate: Date.now(),
            bankAccountNumber: undefined,
            incomeAmount: 0,
            expenseAmount: 0,
            counterpartyName: '',
            fundDailyDimensionId: undefined,
            taxDetail: '',
            developmentCostDetail: '',
            projectId: projectId,
            purposeDetail: '',
            counterpartyAccountNumber: '',
            dataStatus: 1,
            createdBy: editor,
            editable: true
        });
        hasEditableRow.value = data.value.some(row => row.editable);
    }

    // 删除一行
    async function deleteRow(row: EditableFygDataSource, index: number) {
        if (!row.id) {
            data.value.splice(index, 1)
            hasEditableRow.value = data.value.some(row => row.editable);
        }
        else {
            const url = BASE_SERVER + 'update-status';
            const params = {
                id: row.id,
                dataStatus: 0,
                updatedBy: editor
            }
            const result = await deleteById(url, params);
            if (result) {
                message.success('删除成功');
                data.value.splice(index, 1);
                // 检查是否还有可编辑行
                hasEditableRow.value = data.value.some(row => row.editable);
            } else {
                message.error('删除失败');
            }
        }
    }

    // 处理粘贴数据
    async function handlePaste(event: ClipboardEvent) {
        const clipboardData = event.clipboardData;
        if (clipboardData) {
            const text = clipboardData.getData('text');
            if (text) {
                const rows = text.split('\n').filter(row => row.trim() !== '');
                const header = rows[0].split('\t');
                const mapping: Record<string, string> = {
                    '交易日期': 'transactionDate',
                    '银行账号': 'bankAccountNumber',
                    '收入金额(元)': 'incomeAmount',
                    '支出金额(元)': 'expenseAmount',
                    '对方户名': 'counterpartyName',
                    '对方账号': 'counterpartyAccountNumber',
                    '对方类型': 'counterpartyType',
                    '资金科目名称': 'fundDailyDimensionId',
                    '年利率': 'annualInterestRate',
                    '资金用途': 'purposeDetail',
                    '收支二级明细': 'secondaryDetail',
                    '收支三级明细': 'tertiaryDetail',
                    '备注': 'remark'
                };

                // 检查列名是否符合要求
                // 查找未匹配的列
                const unmatchedColumns = header.filter(column => !mapping[column.trim()]);
                const matchedColumns = header.filter(column => mapping[column.trim()]);

                // 如果没有任何列名匹配，直接返回并提示错误
                if (matchedColumns.length === 0) {
                    message.error('粘贴的数据格式不正确，未找到任何符合要求的列名');
                    return;
                }

                // 如果有未匹配的列名，提示用户
                if (unmatchedColumns.length > 0) {
                    message.error(`以下列名不符合要求: ${unmatchedColumns.join(', ')}`);
                }

                for (let i = 1; i < rows.length; i++) {
                    const cells = rows[i].split('\t');
                    const newRow: EditableFygDataSource = {
                        id: undefined,
                        tempId: uuidv4(),  // 使用 UUID 生成唯一的临时 ID
                        transactionDate: Date.now(),
                        bankAccountNumber: undefined,
                        incomeAmount: 0,
                        expenseAmount: 0,
                        counterpartyName: '',
                        fundDailyDimensionId: undefined,
                        taxDetail: '',
                        developmentCostDetail: '',
                        projectId: projectId,
                        purposeDetail: '',
                        counterpartyAccountNumber: '',
                        dataStatus: 1,
                        createdBy: editor,
                        editable: true
                    };

                    header.forEach((column, index) => {
                        const key = mapping[column.trim()];
                        if (key) {
                            switch (key) {
                                case 'transactionDate':
                                    const dateString = cells[index].trim();
                                    if (/^\d{8}$/.test(dateString)) {
                                        // 格式为 YYYYMMDD
                                        const year = parseInt(dateString.slice(0, 4), 10);
                                        const month = parseInt(dateString.slice(4, 6), 10) - 1;
                                        const day = parseInt(dateString.slice(6, 8), 10);
                                        newRow.transactionDate = new Date(year, month, day).getTime();
                                    } else {
                                        newRow.transactionDate = Date.parse(dateString) || Date.now();
                                    }
                                    break;
                                case 'incomeAmount':
                                    newRow.incomeAmount = parseNumber(cells[index]);
                                    break;
                                case 'expenseAmount':
                                    newRow.expenseAmount = parseNumber(cells[index]);
                                    break;
                                case 'annualInterestRate':
                                    newRow.annualInterestRate = parseNumber(cells[index]);
                                    break;
                                case 'bankAccountNumber':
                                    const bank = banks.value.find(bank => bank.accountNumber === cells[index].trim());
                                    if (bank) {
                                        newRow.bankAccountNumber = bank.accountNumber;
                                        newRow.bankAbbreviation = bank.abbreviation;
                                        newRow.bankAccountName = bank.bankName;
                                        newRow.bankAccountId = bank.id;
                                    }
                                    break;
                                case 'fundDailyDimensionId':
                                    const fundDailyTarget = fundDailyFilterDimensionOptions.find(option => option.label === cells[index].trim());
                                    newRow.fundDailyDimensionId = fundDailyTarget ? fundDailyTarget.value : undefined;
                                    break;
                                case 'counterpartyType':
                                    const counterpartyTypeTarget = counterpartyTypeOptions.find(option => option.label === cells[index].trim());
                                    newRow.counterpartyType = counterpartyTypeTarget ? counterpartyTypeTarget.value : undefined;
                                    break;
                                default:
                                    newRow[key] = cells[index];
                                    break;
                            }
                        }
                    });
                    data.value.unshift(newRow);
                }
                hasEditableRow.value = data.value.some(row => row.editable);
            }
        }
    }

    function parseNumber(value: string) {
        const parsedValue = parseFloat(value.replace(/,/g, ''));
        // 检查是否为 NaN，若是则设置为默认值（如 0），否则返回解析后的数字
        return isNaN(parsedValue) ? 0 : parsedValue;
    }

    // 监听粘贴事件
    onMounted(() => {
        window.addEventListener('paste', handlePaste);
        getBanks();
        handleSearch(false);
    });

    // 从剪贴板粘贴按钮点击事件
    function handlePasteFromClipboard() {
        navigator.clipboard.readText().then(text => {
            if (text) {
                const event = new ClipboardEvent('paste', {
                    clipboardData: new DataTransfer()
                });
                event.clipboardData?.setData('text', text);
                handlePaste(event);
            }
        }).catch(err => {
            console.error('Failed to read clipboard contents: ', err);
        });
    }

    function findLabelByValue(value: number): string | undefined {
        for (const group of fundDailyDimensionOptions) {
            if ('children' in group) {
                const option = (group.children as SelectOption[]).find(child => child.value === value);
                if (option && typeof option.label === 'string') {
                    return option.label;
                }
            }
        }
        return undefined;
    }
</script>

<style scoped>
    /* 表格页面样式 */
</style>
