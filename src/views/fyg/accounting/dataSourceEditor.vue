<template>
    <n-card title="编辑数据源" size="small" :style="cardStyle">
        <div class="custom-display">
            <data-source-edit ref="sc" :width="cardWidth" />
        </div>
        <div class="fixed-bottom">
                <n-space style="width: 200px;">
                    <n-button type="primary" size="small" @click="handleSubmit">提交</n-button>
                    <n-button type="info" size="small" @click="goBack">返回</n-button>
                </n-space>
            </div>
    </n-card>
</template>
<script setup lang="ts">
    import { ref } from 'vue'
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    import DataSourceEdit from '../../../components/fyg/accounting/dataSource/DataSourceEdit.vue';


    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    // 导入 useTableDimensions
    const { cardStyle, width, } = useTableDimensions();
    const cardWidth = ref(100)
    watch(() => width.value, (newValue) => {
        cardWidth.value = newValue
        console.log(`output->cardWidth.value`,cardWidth.value)
    })

    const sc = ref<any>(null);

    const handleSubmit = (e: MouseEvent) => {
        console.log('handleSubmit called'); // Debug log
        sc.value.doSave(e)
    };
</script>
<style scoped>
    .n-card {
        width: 100%;
    }

    .custom-display {
        display: flex;
        justify-content: center;
    }

    .fixed-bottom {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 20px;
        margin: auto;
        width: 100px;
        /* 你可以根据需要设置宽度 */
        text-align: center;
        /* background-color: #f8f9fa; */
        background-color: transparent;
        /* 背景颜色 */
        padding: 4px;
        border-radius: 10px;
        box-shadow: 0 0 0px rgba(0, 0, 0, 0.2);
    }
</style>