<template>
    <n-space :style="spaceStyle">
        <n-button-group>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <n-data-table :columns="columns" :data="data" :single-line="false" :pagination="pagination" :style="tableStyle"
        :max-height="tableMaxHeight" />
</template>

<script setup lang="ts">
    import { h, ref } from 'vue'
    import { NButton, NSpace, NSelect } from 'naive-ui'
    import type { DataTableColumns } from 'naive-ui'
    import type { CounterpartyAccountProjectType, } from '../../../components/fyg/accounting/counterparty'
    import { counterpartyAccountTypeOptions } from '../../../components/fyg/accounting/counterparty'
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyle } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })
    const projects = [
        { label: '合璟府', value: 101 },
        { label: '天合府', value: 102 },
    ]

    const counterpartyAccounts = [{ label: '方远房地产集团有限公司', value: 1 }, { label: '台州市路桥区路南街道方林村经济合作社', value: 2 }]


    function createData(): EditableCounterpartyAccountProjectType[] {
        return [
            {
                id: 1,
                projectId: 101,
                counterpartyAccountId: 1,
                type: '股东方',
                dataStatus: 1,
                createdAt: new Date('2024-02-01').getTime(),
                updatedAt: new Date('2024-02-02').getTime(),
                createdBy: 'Admin',
                updatedBy: 'Admin',
                editable: false
            },
            {
                id: 2,
                projectId: 101,
                counterpartyAccountId: 2,
                type: '股东方',
                dataStatus: 1,
                createdAt: new Date('2024-02-01').getTime(),
                updatedAt: new Date('2024-02-02').getTime(),
                createdBy: 'Admin',
                updatedBy: 'Admin',
                editable: false
            },
            {
                id: 3,
                projectId: 102,
                counterpartyAccountId: 1,
                type: '股东方',
                dataStatus: 1,
                createdAt: new Date('2024-02-01').getTime(),
                updatedAt: new Date('2024-02-02').getTime(),
                createdBy: 'Admin',
                updatedBy: 'Admin',
                editable: false
            },
        ]
    }

    const data = ref(createData())

    interface EditableCounterpartyAccountProjectType extends CounterpartyAccountProjectType {
        editable?: boolean
    }

    const createColumns = (): DataTableColumns<EditableCounterpartyAccountProjectType> => [
        {
            title: '项目',
            key: 'projectId',
            render(row, index) {
                return row.editable
                    ? h(NSelect, {
                        options: projects,
                        value: row.projectId,
                        onUpdateValue(v) {
                            data.value[index].projectId = Number(v)
                        }
                    })
                    : projects.find(project => project.value === row.projectId)?.label || row.projectId
            }
        },
        {
            title: '对方户名',
            key: 'counterpartyAccountId',
            render(row, index) {
                return row.editable
                    ? h(NSelect, {
                        options: counterpartyAccounts,
                        value: row.counterpartyAccountId,
                        onUpdateValue(v) {
                            data.value[index].counterpartyAccountId = Number(v)
                        }
                    })
                    : counterpartyAccounts.find(d => d.value === row.counterpartyAccountId)?.label || row.counterpartyAccountId
            }
        },
        {
            title: '类型',
            key: 'type',
            render(row, index) {
                return row.editable
                    ? h(NSelect, {
                        options: counterpartyAccountTypeOptions,
                        value: row.type,
                        onUpdateValue(v) {
                            data.value[index].type = v
                        }
                    })
                    : row.type
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 180,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(index)
                        }, { default: () => '删除' })
                    ]
                })
            }
        }
    ]

    const columns = createColumns()

    const pagination = {
        pageSize: 10
    }

    // 切换编辑状态
    function toggleEdit(index: number) {
        data.value[index].editable = !data.value[index].editable
    }

    // 新增一行
    function addRow() {
        data.value.push({
            id: undefined, // 新增行的id
            projectId: undefined,
            counterpartyAccountId: undefined,
            type: '',
            dataStatus: 1,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            createdBy: 'Admin',
            updatedBy: 'Admin',
            editable: true // 新增行处于可编辑状态
        })
    }

    // 删除一行
    function deleteRow(index: number) {
        data.value.splice(index, 1)
    }
</script>

<style scoped>
    /* 表格页面样式 */
</style>
