<template>
    <n-space style="display: flex; justify-content: right;margin: 4px 4px 4px 4px;">
        <n-button @click="handleSearch">新增</n-button>
    </n-space>
    <n-data-table ref="ftable" remote :columns="pageData.columns.column" :data="pageData.data.content" :style="tableStyle"
        :loading="loading" :pagination="paginationReactive" :bordered="false" @update-filters="combinedHandler" />
</template>
<script setup lang="ts">
    import { fetchData, getPermission, } from '../../../utils/APIUtils';
    import { fetchTestData, DEFAULT_SETTING, } from '../../../components/fyg/accounting/counterparty';
    import { doPostAsync } from '../../../utils/AxiosUtil';
    import type { QueryParam, TableData, } from '../../../components/fyg/accounting/counterparty';
    import type { ExtendedDataTableBaseColumn, SETTING } from '../../../utils/TableUtils';
    import { createColumnWithTemplate } from '../../../utils/TableUtils';
    import {
        DataTableBaseColumn,
        DataTableFilterState,
        NButton,
    } from 'naive-ui'
    import { inject } from 'vue';

    const BASE_SERVER = '/fyg/projects/';

    type FilterData = {
        key: string,
        value: any
    }
    const doFilter = async (val: FilterData) => {
        if (Array.isArray(val.value)) {
            let raw = normalizeValues(val.value);
            queryParam[val.key] = JSON.stringify(raw);
        } else {
            queryParam[val.key] = val.value;
        }
        await handleSearch()
    }

    type Field = {
        key: string,
        data: ExtendedDataTableBaseColumn,
        type: string,
        toDo?: Function,
    }

    const fields: Field[] = [{
        key: 'accountName',
        data: { title: '对方户名', key: 'accountName', width: 400, isActivated: true },
        type: 'text',
        toDo: doFilter
    },]

    const actions = {
        title: '操作',
        key: 'actions',
        width: 180,
        render(row:any) {
            return h(
                NButton,
                {
                    strong: true,
                    size: 'small',
                    type: 'primary',
                    onClick() {
                        message.info('编辑');
                    },
                }, {
                    default: () => '编辑'
                }
            )
        },
        isActivated: true
    }
    // 定义 setColumn 函数，根据 userSetting 动态生成 columns
    const setColumn = (settings: SETTING[]) => {
        let columnsArray: ExtendedDataTableBaseColumn[] = [];
        let columnsMap: any = {};

        settings.forEach((setting) => {
            if (setting.isActivated) {
                let column: any;
                const field = fields.find(f => f.key === setting.key);
                if (field) {
                    if (field.type === 'text') {
                        column = createColumnWithTemplate(field.data, field.type, field.toDo);
                    }
                    // 添加到数组
                    columnsArray.push(column);
                    
                    // 同时添加到映射
                    columnsMap[field.data.key] = column;
                }
            }
        });
        columnsArray.push(actions);
        return { columnsArray, columnsMap };
    }

    // 获取列数据
    const userSetting = reactive<SETTING[]>(DEFAULT_SETTING);
    const { columnsArray, columnsMap } = setColumn(userSetting);

    //table 
    const ftable = ref<any>(null);
    const loadingRef = ref(true)
    const loading = loadingRef
    const columns = reactive<any>({
        column: []
    })

    //filter
    const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        const key = initiatorColumn?.key;
        if (key && filters[key]) {
            // Ensure the values are treated as an array of strings
            const filterValues = filters[key];
            let filteredValues: string[] = [];

            if (Array.isArray(filterValues)) {
                // Assuming FilterOptionValue can be directly used as string
                filteredValues = filterValues.map(value => String(value));
            } else if (filterValues != null) {
                // Handle a single FilterOptionValue or non-array
                filteredValues = [String(filterValues)];
            }

            // Now filteredValues is guaranteed to be string[], can use it safely
            if (filteredValues.length > 0) {
                let raw = normalizeValues(filteredValues);
                queryParam[key + 'JsonStr'] = JSON.stringify(raw);
                handleSearch();
            } else {
                delete queryParam[key + 'JsonStr'];
                handleSearch();
            }
            columnsMap[key].filterOptionValues = filters[key]
        }
    }

    function normalizeValues(values: string[]): (number | string | null)[] {
        return values.map(value => {
            // 检查是否为数字类型的字符串，如果是，则转换为数字
            if (/^\d+$/.test(value)) {
                return Number(value);
            } else {
                //不是数字的直接返回就好了
                return value;
            }
        }).filter(item => item !== null);
    }

    const anotherHandler = (filters: any) => {
        console.log('Second handler:', filters);
        // 其他处理逻辑
    };

    // 合并这两个处理器到一个事件监听器
    const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        handleFilters(filters, initiatorColumn);
        anotherHandler(filters);
    };

    const data = reactive<TableData>({});

    const initialPaginationState = {
        itemCount: 0,
        pageCount: 1,
        page: 1,
        pageSize: 15,
        showSizePicker: true,
        pageSizes: [15, 25, 50],
        onChange: (page: number) => {
            paginationReactive.page = page;
            handleSearch();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize
            paginationReactive.page = 1
            handleSearch();
        },
        prefix({ itemCount }: { itemCount?: number }) {
            return `Total is ${itemCount}.`
        }
    }

    const paginationReactive = reactive({ ...initialPaginationState });

    // 定义重置方法
    async function resetPagination() {
        Object.assign(paginationReactive, { ...initialPaginationState });
    }

    const pageData = {
        data: data,
        columns: columns,
        pagination: paginationReactive
    }

    //query
    const initialQueryParam: QueryParam = {
        dataStatus: 0,
        currentPage: paginationReactive.page - 1,
        pageSize: paginationReactive.pageSize,
        projectName: undefined,
        columnKey: 'accountName',
        order: 'descend'
    };
    const queryParam = reactive<QueryParam>({ ...initialQueryParam });

    async function resetQueryParam() {
        Object.assign(queryParam, { ...initialQueryParam });
    }

    //data
    async function getData(queryParam?: QueryParam) {
        const url = BASE_SERVER + 'filter'
        // const results = await fetchData(url, queryParam);
        // if (results.length > 0) {
        //     const result = results[0];
        //     result.content.forEach((item: Project) => {
        //         Object.keys(item).forEach(key => {
        //             // 仅当项的值不是 null 或 undefined 时才继续处理
        //             if (item[key] != null && typeof item[key] === 'string') {
        //                 const trimmedValue = item[key].trim();
        //                 // 如果字符串不为空，尝试转换为数字
        //                 if (trimmedValue !== '') {
        //                     const convertedNumber = Number(trimmedValue);
        //                     // 只有当转换结果为有效数字时，才更新该值
        //                     if (!isNaN(convertedNumber)) {
        //                         item[key] = convertedNumber;
        //                     }
        //                 }
        //             }
        //         });
        //     });

        //     Object.assign(data, {
        //         ...result  // 确保从服务器返回的结果是完整的，包括所有需要的字段
        //     });
        // }
        // 获取项目数据
        fetchTestData().then((result) => {
            console.log('fetchProjects response:', result); // Debug log
            Object.assign(data, {
                ...result
            });
        });
        paginationReactive.pageCount = data.totalPages ? data.totalPages : 0;
        paginationReactive.itemCount = data.totalElements ? data.totalElements : 0;
        setTimeout(() => {
            loadingRef.value = false
        }, 1000);
    }

    const handleSearch = async () => {
        loadingRef.value = true
        queryParam.pageSize = paginationReactive.pageSize;
        queryParam.currentPage = paginationReactive.page - 1;
        // queryParam.permissionId = permissionId.value;
        await getData(queryParam);
    }

    //window size
    const Window: any = window
    type MyWindow = {
        width: number,
        height: number
    }
    const myWindow = reactive<MyWindow>({
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
    })

    // card 样式
    type TableStyle = {
        height: string;

    };
    const tableStyle = reactive<TableStyle>({
        height: myWindow.height - 72 + 'px',
    });

    watch(myWindow, (newValue, oldValue) => {
        tableStyle.height = newValue.height - 72 + 'px'
    })

    const getColumns = () => {
        Object.assign(userSetting, DEFAULT_SETTING);
        const { columnsArray, columnsMap } = setColumn(userSetting);
        Object.assign(columns, { column: columnsArray });
    }

    onMounted(() => {
        Window.onresize = () => {
            return (() => {
                Window.fullHeight = document.documentElement.clientHeight
                Window.fullWidth = document.documentElement.clientWidth
                myWindow.height = Window.fullHeight
                myWindow.width = Window.fullWidth
            })()
        }
        getColumns();
        handleSearch();
    })

    // 从全局注入中获取 message 实例
    const message: any = inject('message');
</script>