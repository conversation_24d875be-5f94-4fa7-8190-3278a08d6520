<template>
    <n-space :style="spaceStyleWithoutJustify" justify="space-between">
        <n-h3>资金日报大表</n-h3>
        <n-button-group>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <n-data-table remote :columns="columns" :data="data" :loading="loading" :style="tableStyle" :bordered="true"
        @update-filters="combinedHandler" @scroll="handleScroll" :single-line="false" :virtual-scroll="!hasEditableRow"
        :scroll-x="tableWidth" :max-height="tableMaxHeight" />
</template>

<script setup lang="ts">
    import { ref, reactive, onMounted, h } from 'vue';
    import MyDatePicker from '../../../components/MyDateFilterComponent.vue';
    import MyNumberPicker from '../../../components/MyNumberFilterComponent.vue';
    import { normalizeValues, formatDate } from '../../../utils/xUtils';
    import { useRoute, useRouter } from 'vue-router';
    import { NSpace, NButton, NInputNumber, NIcon, NSelect, NDatePicker } from 'naive-ui';
    import { DataTableColumns, DataTableBaseColumn, DataTableFilterState } from 'naive-ui';
    import { Search as IconSearch } from '@vicons/ionicons5';
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    import { fetchDataAlt, deleteById, } from '../../../utils/APIUtils';
    import { doPutAsync } from '../../../utils/AxiosUtil';
    import { DailyReportSummaryDTO as EditableTableData, DailyReportSummaryQuery as QueryParam } from '../../../components/fyg/accounting/dailyReportSummary';
    import { useMessage } from 'naive-ui';

    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const editor = vueCookies.get('account');
    const route = useRoute();
    const router = useRouter();
    const message = useMessage();

    // 页面参数和 API 基础路径
    // const projectId = Number(route.params.id);
    const BASE_SERVER = '/fyschedule/fyg/accounting/report/daily/summary/';

    //获取项目列表
    interface Options {
        label: string;
        value: number;
    }
    const projects = ref<Options[]>([])
    async function getProjectList() {
        const url = '/fyschedule/fyg/accounting/project/list-options';
        const response = await fetchDataAlt(url, { dataStatus: 1 });
        if (response.length > 0) {
            projects.value = response;

            // 更新列的 filterOptions
            projectNameColumn.filterOptions = projects.value;
        }
    }

    // 表格样式及分页
    const { tableStyle, spaceStyleWithoutJustify, width, maxHeight } = useTableDimensions();
    const tableMaxHeight = ref(100);
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue - 44;
    });
    const tableWidth = ref(2000);
    watch(() => width.value, (newValue) => {
        const total = calculateTotalColumnWidth(columns);
        tableWidth.value = total + 200; // 增加一定的宽度以避免滚动条遮挡
    });
    function calculateTotalColumnWidth(columns: DataTableColumns<EditableTableData>) {
        return columns.reduce((totalWidth, column) => {
            // 设置默认宽度 150
            const columnWidth = typeof column.width === 'number' ? column.width : 150;
            return totalWidth + columnWidth;
        }, 0);
    }

    // 数据和分页
    const data = ref<EditableTableData[]>([]);
    const loading = ref(false);
    const pagination = reactive({
        itemCount: 0,
        pageCount: 1,
        page: 1,
        pageSize: 15,
    });

    // 查询参数
    const queryParam = reactive<QueryParam>({
        dataStatus: 1,
        order: 'ascend',
        columnKey: 'sequenceNumber',
        currentPage: pagination.page - 1,
        pageSize: pagination.pageSize,
    });

    // 切换页面
    const goBack = () => router.back();

    const setCurrentPage = async (page: number) => {
        queryParam.currentPage = page - 1;
    }
    // 加载数据
    const handleSearch = async () => {
        loading.value = true;
        const url = `${BASE_SERVER}filter`;
        await setCurrentPage(pagination.page);
        const result = await fetchDataAlt(url, queryParam);
        if (result) {
            // data.value = result[0].content || [];
            result[0].content.forEach((item: EditableTableData) => {
                data.value.push(item);
            });
            pagination.itemCount = result[0].totalElements || 0;
            pagination.pageCount = result[0].totalPages || 0;
        }
        loading.value = false;
    };

    // 保存数据
    const saveData = async (row: EditableTableData, index: number) => {
        const url = `${BASE_SERVER}save`;
        row.updatedBy = editor;
        const response: any = await doPutAsync(url, row);
        if (response && response.code === 200) {
            Object.assign(data.value[index], response.data);
            message.success('保存成功');
        } else {
            message.error('保存失败');
        }
    };

    // 新增行
    const addRow = () => {
        data.value.unshift({
            id: undefined,
            sequenceNumber: data.value.length + 1,
            projectId: undefined,
            dataStatus: 1,
            createdBy: editor,
            editable: true,
        });
        // 检查是否还有可编辑行
        hasEditableRow.value = data.value.some(row => row.editable);
    };

    // 同步数据
    async function syncData(row: EditableTableData, index: number) {
        const url = `${BASE_SERVER}sync`;
        const resp: any = await doPutAsync(url, row);
        if (resp && resp.code === 200) {
            message.success('同步成功');
            data.value[index] = { ...resp.data }
            data.value[index].editable = true;
        }
    }
    // 删除行
    const deleteRow = async (row: any, index: number) => {
        if (!row.id) {
            data.value.splice(index, 1);
            // 检查是否还有可编辑行
            hasEditableRow.value = data.value.some(row => row.editable);
        } else {
            const url = `${BASE_SERVER}update-status`;
            const result = await deleteById(url, { id: row.id, dataStatus: 0, updatedBy: editor });
            if (result) {
                message.success('删除成功');
                data.value.splice(index, 1);
                // 检查是否还有可编辑行
                hasEditableRow.value = data.value.some(row => row.editable);
            } else {
                message.error('删除失败');
            }
        }
    };

    // 滚动加载
    const handleScroll = async (e: Event) => {
        if (hasEditableRow.value) {
            console.log('存在可编辑行，滚动加载数据暂停');
            return; // 如果有可编辑行，直接返回
        }
        const target = e.target as HTMLElement;

        // 判断是否是垂直方向滚动
        if (target.scrollTop === 0 && target.scrollHeight === target.clientHeight) {
            return; // 没有垂直滚动
        }
        // 定义一个容忍阈值
        const threshold = 1; // 允许 1 像素误差，避免因小数而误判
        // 计算是否已经滑动到底部
        const isBottom = Math.abs(target.scrollHeight - target.scrollTop - target.clientHeight) <= threshold;
        
        if (isBottom && pagination.page < pagination.pageCount) {
            
            pagination.page = pagination.page + 1;
            await handleSearch();
        } else {
            console.log('已加载全部数据');
        }
    };

    // 切换编辑状态
    const hasEditableRow = ref(false);
    const toggleEdit = (row: EditableTableData, index: number) => {
        if (row.editable) {
            saveData(row, index);
        }
        row.editable = !row.editable;
        // 检查是否还有可编辑行
        hasEditableRow.value = data.value.some(row => row.editable);
    };

    const sequenceNumberColumn: DataTableBaseColumn<EditableTableData> = reactive({
        title: '序号',
        key: 'sequenceNumber',
        width: 100, // 必须设置宽度
        render(row) {
            return row.editable
                ? h(NInputNumber, {
                    value: row.sequenceNumber,
                    onUpdateValue: (v) => (row.sequenceNumber = v || undefined),
                })
                : row.sequenceNumber;
        },
    });

    const projectNameColumn: DataTableBaseColumn<EditableTableData> = reactive({
        title: '项目名称',
        key: 'projectId',
        width: 150,
        filter: true,
        filterMultiple: true,
        filterOptionValues: [],
        filterOptions: projects.value,
        render(row, index) {
            return row.editable
                ? h(NSelect, {
                    options: projects.value,
                    value: row.projectId,
                    onUpdateValue: (v) => {
                        row.projectName = projects.value.find(option => option.value === v)?.label;
                        row.projectId = v;
                    },
                })
                : projects.value.find(option => option.value === row.projectId)?.label;
        },
    });


    const pureEquityRatioColumn: DataTableBaseColumn<EditableTableData> = reactive({
        title: '纯股比',
        key: 'pureEquityRatio',
        width: 150, // 必须设置宽度
        render(row) {
            return row.editable
                ? h(NInputNumber, {
                    value: row.pureEquityRatio,
                    precision: 2,
                    showButton: false,
                    onUpdateValue: (v) => (row.pureEquityRatio = Number(v)),
                })
                : `${row.pureEquityRatio?.toFixed(2) || 0}%`;
        },
    });

    type FilterData = {
        key: string,
        value: any
    }
    const doFilter = async (val: FilterData) => {
        if (Array.isArray(val.value)) {
            let raw = normalizeValues(val.value);
            queryParam[val.key] = JSON.stringify(raw);
        } else {
            queryParam[val.key] = val.value;
        }
        await handleSearch()
    }

    const dueDateFilterColumn: DataTableBaseColumn<EditableTableData> = reactive({
        title: '到期日',
        key: 'dueDate',
        width: 200,
        filter: true,
        filterOptionValue: null,
        renderFilterIcon: () => {
            return h(NIcon, null, { default: () => h(IconSearch) });
        },
        renderFilterMenu: ({ hide }) => {
            return h(MyDatePicker, {
                hide, type: 'datePicker', filterOptionValue: dueDateFilterColumn.filterOptionValue, onFilter: (val: any) => {
                    if (val) {
                        dueDateFilterColumn.filterOptionValue = val;
                    } else {
                        dueDateFilterColumn.filterOptionValue = null;
                    }
                    doFilter({ key: 'dueDateJsonStr', value: val });
                }
            });
        },
        render(row, index) {
            return row.editable
                ? h(NDatePicker, {
                    value: row.dueDate,
                    type: 'date',
                    onUpdateValue(v) {
                        data.value[index].dueDate = new Date(v).getTime();
                    }
                })
                : formatDate(row.dueDate, 'YYYY-MM-DD');
        }
    });

    const createNumberColumn = (title: string, key: string, width?: number, mark?: string): DataTableBaseColumn<EditableTableData> => {
        const column: DataTableBaseColumn<EditableTableData> = reactive({
            title,
            key,
            width: width ? width : 150,
            filter: true,
            filterOptionValue: null,
            renderFilterIcon: () => {
                return h(NIcon, null, { default: () => h(IconSearch) });
            },
            renderFilterMenu: ({ hide }) => {
                return h(MyNumberPicker, {
                    hide, type: 'numberPicker', filterOptionValue: column.filterOptionValue, onFilter: (val: any) => {
                        if (val) {
                            column.filterOptionValue = val;
                        } else {
                            column.filterOptionValue = null;
                        }
                        doFilter({ key: key + 'JsonStr', value: val });
                    }
                });
            },
            render(row, index) {
                return row.editable
                    ? h(NInputNumber, {
                        value: row[key],
                        showButton: false,
                        precision: 2,
                        onUpdateValue(v) {
                            data.value[index][key] = Number(v);
                        }
                    }, {
                        suffix: () => mark
                    })
                    : `${row[key]?.toFixed(2) || 0.00}${mark || ''}`;
            }
        });
        return column;
    };

    const realEstateInputColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('房产投入', 'realEstateInput', 150, '元');
    const realEstateRecoveredColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('房产回款', 'realEstateRecovered', 150, '元');
    const actualUsedRealEstateFundsColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('实际占用房产资金', 'actualUsedRealEstateFunds', 150, '元');
    const partnerFundsUsedColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('合作股东占用资金', 'partnerFundsUsed', 150, '元');
    const fundBalanceColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('资金余额', 'fundBalance', 150, '元');
    const supervisedAccountBalanceColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('监管户余额', 'supervisedAccountBalance', 150, '元');
    const loanBalanceColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('贷款余额', 'loanBalance', 150, '元');
    const saleValueWithoutBuybackColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('销售货值（不含回购）', 'saleValueWithoutBuyback', 180, '元');
    const saleAmountWithoutBuybackColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('销售金额（不含回购）', 'saleAmountWithoutBuyback', 180, '元');
    const receivedSalesFundsWithoutBuybackColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('已收销售款（不含回购）', 'receivedSalesFundsWithoutBuyback', 200, '元');
    const recoverableFundsWithoutBuybackColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('已售可收（不含回购）', 'recoverableFundsWithoutBuyback', 180, '元');
    const buybackValueColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('回购货值', 'buybackValue', 150, '元');
    const reachedBuybackNodeColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('节点已到回购（类销售金额）', 'reachedBuybackNode', 220, '元');
    const receivedBuybackFundsColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('已收回购款', 'receivedBuybackFunds', 150, '元');
    const recoverableBuybackFundsColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('可收回购款', 'recoverableBuybackFunds', 150, '元');
    const operatingExpenditureColumn: DataTableBaseColumn<EditableTableData> = createNumberColumn('经营支出', 'operatingExpenditure', 150, '元');


    // 创建列
    const columnsMap: any = {
        projectId: projectNameColumn
    }
    const createColumns = (): DataTableColumns<EditableTableData> => reactive([
        sequenceNumberColumn,
        projectNameColumn,
        pureEquityRatioColumn,
        realEstateInputColumn,
        realEstateRecoveredColumn,
        actualUsedRealEstateFundsColumn,
        partnerFundsUsedColumn,
        fundBalanceColumn,
        supervisedAccountBalanceColumn,
        loanBalanceColumn,
        dueDateFilterColumn,
        saleValueWithoutBuybackColumn,
        saleAmountWithoutBuybackColumn,
        receivedSalesFundsWithoutBuybackColumn,
        recoverableFundsWithoutBuybackColumn,
        buybackValueColumn,
        reachedBuybackNodeColumn,
        receivedBuybackFundsColumn,
        recoverableBuybackFundsColumn,
        operatingExpenditureColumn,
        {
            title: '操作',
            key: 'action',
            width: 198,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(row, index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'info',
                            disabled: row.projectId ? false : true,
                            onClick: () => syncData(row, index)
                        }, { default: () => '同步数据' }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(row, index)
                        }, { default: () => '删除' })
                    ]
                });
            },
            fixed: 'right',
        },
    ]);

    const columns = createColumns();

    //filter
    const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        const key = initiatorColumn?.key;
        if (key && filters[key]) {
            // Ensure the values are treated as an array of strings
            const filterValues = filters[key];
            let filteredValues: string[] = [];

            if (Array.isArray(filterValues)) {
                // Assuming FilterOptionValue can be directly used as string
                filteredValues = filterValues.map(value => String(value));
            } else if (filterValues != null) {
                // Handle a single FilterOptionValue or non-array
                filteredValues = [String(filterValues)];
            }

            // Now filteredValues is guaranteed to be string[], can use it safely
            if (filteredValues.length > 0) {
                let raw = normalizeValues(filteredValues);
                queryParam[key + 'JsonStr'] = JSON.stringify(raw);
                handleSearch();
            } else {
                delete queryParam[key + 'JsonStr'];
                handleSearch();
            }
            columnsMap[key].filterOptionValues = filters[key]

        }
    }

    const anotherHandler = (filters: any) => {
        console.log('Second handler:', filters);
        // 其他处理逻辑
    };

    // 合并这两个处理器到一个事件监听器
    const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        handleFilters(filters, initiatorColumn);
        anotherHandler(filters);
    };

    onMounted(() => {
        handleSearch();
        getProjectList();
    });
</script>

<style scoped>
    /* 表格样式 */
</style>