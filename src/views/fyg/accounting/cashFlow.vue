<template>
    <div :style="{
        display: 'flex',
        width: '100%',
        boxSizing: 'border-box',
        padding: '0px',
        backgroundColor: 'var(--color-fill-2)',
    }">
        <n-space>
            <n-table-cash-flow-statement></n-table-cash-flow-statement>
            <n-table-operating-cash-flow></n-table-operating-cash-flow>
        </n-space>
    </div>
</template>
<script lang="ts" setup>
    import { ref } from 'vue'
    import NTableCashFlowStatement from '../../../components/fyg/accounting/cashFlow/NTableCashFlowStatement.vue';
    import NTableOperatingCashFlow from '../../../components/fyg/accounting/cashFlow/NTableOperatingCashFlow.vue';
</script>