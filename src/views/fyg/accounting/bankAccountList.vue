<template>
    <n-space :style="spaceStyleWithoutJustify" justify="space-between">
        <n-h3>{{ name }}</n-h3>
        <n-button-group>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <n-data-table :columns="columns" :data="data" :pagination="pagination" :style="tableStyle" :loading="loading"
        :max-height="tableMaxHeight" />
</template>

<script setup lang="ts">
    import { h, ref, watch } from 'vue'
    import { NButton, NSpace, NInput, NSelect } from 'naive-ui'
    import type { DataTableColumns } from 'naive-ui'
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const editor = vueCookies.get('account');
    import { decryptRowId, deleteById, fetchDataAlt } from '../../../utils/APIUtils';
    import { doPutAsync } from '../../../utils/AxiosUtil';
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const name: string = route.params?.name as string;
    const encryptedId: string = route.params?.id as string;
    const projectId: number = decryptRowId(encryptedId);
    const message = useMessage()
    const BASE_SERVER = '/fyschedule/fyg/accounting/bank-account/'
    const loading = ref(false)
    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyleWithoutJustify } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })
    const accountTypes = [
        { label: '基本户', value: 'JI_BEN_HU' },
        { label: '监管户', value: 'JIAN_GUAN_HU' },
        { label: '一般户', value: 'YI_BAN_HU' },
        { label: '贷款户', value: 'DAI_KUAN_HU' }
    ]

    function createData(): EditableBankAccountType[] {
        return [
        ]
    }

    const data = ref(createData())

    interface EditableBankAccountType {
        id?: number
        projectId: number
        bankName: string
        accountNumber: string
        abbreviation?: string
        accountType: string
        notes?: string
        dataStatus?: number
        createdAt?: number
        updatedAt?: number
        createdBy?: string
        updatedBy?: string
        editable?: boolean
    }

    const createColumns = (): DataTableColumns<EditableBankAccountType> => [
        // {
        //     title: '项目ID',
        //     key: 'projectId',
        //     render(row, index) {
        //         return row.editable
        //             ? h(NSelect, {
        //                 options: projects,
        //                 value: row.projectId,
        //                 onUpdateValue(v) {
        //                     data.value[index].projectId = Number(v)
        //                 }
        //             })
        //             : projects.find(project => project.value === row.projectId)?.label || row.projectId
        //     }
        // },
        {
            title: '银行名称',
            key: 'bankName',
            render(row, index) {
                return row.editable
                    ? h(NInput, {
                        value: row.bankName,
                        onUpdateValue(v) {
                            data.value[index].bankName = v
                        }
                    })
                    : row.bankName
            }
        },
        {
            title: '账户号码',
            key: 'accountNumber',
            render(row, index) {
                return row.editable
                    ? h(NInput, {
                        value: row.accountNumber,
                        onUpdateValue(v) {
                            data.value[index].accountNumber = v
                        }
                    })
                    : row.accountNumber
            }
        },
        {
            title: '账户简称',
            key: 'abbreviation',
            render(row, index) {
                return row.editable
                    ? h(NInput, {
                        value: row.abbreviation,
                        onUpdateValue(v) {
                            data.value[index].abbreviation = v
                        }
                    })
                    : row.abbreviation
            }
        },
        {
            title: '账户类型',
            key: 'accountType',
            render(row, index) {
                return row.editable
                    ? h(NSelect, {
                        options: accountTypes,
                        value: row.accountType,
                        onUpdateValue(v) {
                            data.value[index].accountType = v
                        }
                    })
                    : accountTypes.find(option => option.value === row.accountType)?.label;
            }
        },
        {
            title: '备注',
            key: 'notes',
            render(row, index) {
                return row.editable
                    ? h(NInput, {
                        value: row.notes,
                        onUpdateValue(v) {
                            data.value[index].notes = v
                        }
                    })
                    : row.notes
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 180,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(row, index)
                        }, { default: () => '删除' })
                    ]
                })
            }
        }
    ]

    const columns = createColumns()

    const pagination = {
        pageSize: 15
    }

    // 切换编辑状态
    function toggleEdit(index: number) {
        if (data.value[index].editable) {
            // 切换为编辑状态时，需要获取当前行数据
            saveData(index);
        }
        data.value[index].editable = !data.value[index].editable
    }

    async function saveData(index: number) {
        loading.value = true;
        const url = BASE_SERVER + 'save';
        data.value[index].updatedBy = editor;
        const response: any = await doPutAsync(url, data.value[index]);
        if (response.code === 200) {
            data.value[index] = { ...response.data }
            message.success(response.message)
        } else {
            message.error(`${response.message} 错误代码: ${response.code}`)
        }
        loading.value = false;
    }

    // 新增一行
    function addRow() {
        data.value.push({
            id: undefined, // 新增行的id
            projectId: projectId,
            bankName: '',
            accountNumber: '',
            accountType: '',
            notes: '',
            dataStatus: 1,
            createdBy: editor,
            editable: true // 新增行处于可编辑状态
        })
    }

    // 删除一行
    async function deleteRow(row: EditableBankAccountType, index: number) {
        if (!row.id) data.value.splice(index, 1)
        else {
            const url = BASE_SERVER + 'update-status';
            const params = {
                id: row.id,
                dataStatus: 0,
                updatedBy: editor
            }
            const result = await deleteById(url, params);
            if (result) {
                message.success('删除成功');
                data.value.splice(index, 1);
            } else {
                message.error('删除失败');
            }
        }
    }

     //data
     async function getData() {
        if (!projectId) return
        loading.value = true;
        const url = BASE_SERVER + 'list';
        const params = {
            dataStatus: 1,
            projectId: projectId
        }
        const results = await fetchDataAlt(url, params);
        if (results.length > 0) {
            data.value = results
        }
        loading.value = false;
    }

    onMounted(() => {
        getData()
    })
</script>

<style scoped>
    /* 表格页面样式 */
</style>
