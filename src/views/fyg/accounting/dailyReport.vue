<template>
  <div class="center-container">
    <n-card :title="name" size="small" content-style="padding: 0;" :style="cardStyle">
      <income-and-expenses :project-id="projectId" :editor="editor" :name="name" />
      <equity-structure :project-id="projectId" />
    </n-card>
  </div>

</template>

<script lang="ts" setup>
  import { reactive, watch, onMounted } from 'vue';
  import IncomeAndExpenses from '../../../components/fyg/accounting/dailyReport/IncomeAndExpenses.vue';
  import EquityStructure from '../../../components/fyg/accounting/dailyReport/EquityStructure.vue';
  import { decryptRowId } from '../../../utils/APIUtils';
  import VueCookies from 'vue-cookies'
  const vueCookies: any = VueCookies
  const editor = vueCookies.get('account');
  import { useRoute, useRouter } from 'vue-router'
  const route = useRoute();
  const router = useRouter();
  const name: string = route.params?.name as string;
  const encryptedId: string = route.params?.id as string;
  const projectId = decryptRowId(encryptedId);

  const goBack = () => {
    router.back(); // 返回上一级页面
  };
  //window
  const Window: any = window
  type MyWindow = {
    width: number,
    height: number
  }
  const myWindow = reactive<MyWindow>({
    width: document.documentElement.clientWidth,
    height: document.documentElement.clientHeight
  })

  // card 样式
  type CardStyle = {
    width: string;
    margin: string;
  };
  const cardStyle = reactive<CardStyle>({
    width: myWindow.width - 100 + 'px',
    margin: '0 auto', // 添加居中
  });

  watch(myWindow, (newValue, oldValue) => {
    cardStyle.width = newValue.width - 100 + 'px'
  })
  onMounted(() => {
    Window.onresize = () => {
      return (() => {
        Window.fullHeight = document.documentElement.clientHeight
        Window.fullWidth = document.documentElement.clientWidth
        myWindow.height = Window.fullHeight
        myWindow.width = Window.fullWidth
      })()
    }
  })
</script>

<style scoped>
  .center-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    background-color: var(--color-fill-2);
  }

</style>