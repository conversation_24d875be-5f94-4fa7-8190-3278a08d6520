<template>
    <n-space :style="spaceStyle">
        <n-button-group>
            <n-button @click="addRow">新增</n-button>
            <n-button @click="goBack">返回</n-button>
        </n-button-group>
    </n-space>
    <n-data-table :columns="columns" :data="data" :single-line="false" :pagination="pagination" :style="tableStyle"
        :max-height="tableMaxHeight" />
</template>

<script setup lang="ts">
    import { h, ref } from 'vue'
    import { NButton, NSpace, NInput } from 'naive-ui'
    import type { DataTableColumns } from 'naive-ui'
    import type { Counterparty, } from '../../../components/fyg/accounting/counterparty'
    import { useRouter } from 'vue-router';

    const router = useRouter();

    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyle } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })

    function createData(): EditableCounterparty[] {
        return [
            {
                id: 1,
                accountName: '方远房地产集团有限公司',
                dataStatus: 1,
                createdAt: new Date('2024-02-01').getTime(),
                updatedAt: new Date('2024-02-02').getTime(),
                createdBy: 'Admin',
                updatedBy: 'Admin',
                editable: false
            },
            {
                id: 2,
                accountName: '台州市路桥区路南街道方林村经济合作社',
                dataStatus: 1,
                createdAt: new Date('2024-02-01').getTime(),
                updatedAt: new Date('2024-02-02').getTime(),
                createdBy: 'Admin',
                updatedBy: 'Admin',
                editable: false
            }
        ]
    }

    const data = ref(createData())

    interface EditableCounterparty extends Counterparty {
        editable?: boolean
    }

    const createColumns = (): DataTableColumns<EditableCounterparty> => [
        {
            title: '对方户名',
            key: 'accountName',
            render(row, index) {
                return row.editable
                    ? h(NInput, {
                        value: row.accountName,
                        onUpdateValue(v) {
                            data.value[index].accountName = v
                        }
                    })
                    : row.accountName
            }
        },
        {
            title: '操作',
            key: 'action',
            width: 180,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(index)
                        }, { default: () => (row.editable ? '保存' : '编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(index)
                        }, { default: () => '删除' })
                    ]
                })
            }
        }
    ]

    const columns = createColumns()

    const pagination = {
        pageSize: 10
    }

    // 切换编辑状态
    function toggleEdit(index: number) {
        data.value[index].editable = !data.value[index].editable
    }

    // 新增一行
    function addRow() {
        data.value.push({
            id: undefined, // 新增行的id
            projectId: 0,
            counterpartyAccountId: 0,
            type: '',
            dataStatus: 1,
            // createdAt: new Date().toISOString(),
            // updatedAt: new Date().toISOString(),
            createdAt: Date.now(),
            updatedAt: Date.now(),
            createdBy: 'Admin',
            updatedBy: 'Admin',
            editable: true // 新增行处于可编辑状态
        })
    }
    // 删除一行
    function deleteRow(index: number) {
        data.value.splice(index, 1)
    }
</script>

<style scoped>
    /* 表格页面样式 */
</style>
