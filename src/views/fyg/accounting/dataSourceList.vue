<template>
    <n-space :style="spaceStyle">
        <n-button-group>
        <n-button @click="addRow">新增</n-button>
        <n-button @click="goBack">返回</n-button> 
    </n-button-group>
    </n-space>
    <n-data-table :columns="columnsArray" :data="data" :single-line="false" :loading="loading"
        :pagination="paginationReactive" :style="tableStyle" :max-height="tableMaxHeight" />
</template>
<script setup lang="ts">
    import { fetchData, getPermission, deserializeColumns, getColorObj } from '../../../utils/APIUtils';
    import { createColumnWithDatePickerTemplate, createColumnWithFilterOptions, createColumnWithTemplate, ExtendedDataTableBaseColumn, SETTING } from '../../../utils/TableUtils';
    import type { FygDataSource, QueryParam } from '../../../components/fyg/accounting/dataSource';
    import { optionsMap } from '../../../components/fyg/accounting/dataSource';
    import { DataTableFilterState, DataTableBaseColumn, NSpace, NButton } from 'naive-ui';
    import { encryptRowId } from '../../../utils/APIUtils';
    // 从全局注入中获取 message 实例
    const message: any = inject('message');
    import { normalizeValues } from '../../../utils/xUtils';
    import { decryptRowId } from '../../../utils/APIUtils';
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const encryptedId: string = route.params?.id as string;
    const projectId = decryptRowId(encryptedId);
    import { useTableDimensions } from '../../../fconfig/useTableDimensions';
    // 导入 useTableDimensions
    const { tableStyle, maxHeight, spaceStyle } = useTableDimensions();
    const tableMaxHeight = ref(100)
    watch(() => maxHeight.value, (newValue) => {
        tableMaxHeight.value = newValue
    })

    //filter
    const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        const key = initiatorColumn?.key;
        if (key && filters[key]) {
            // Ensure the values are treated as an array of strings
            const filterValues = filters[key];
            let filteredValues: string[] = [];

            if (Array.isArray(filterValues)) {
                // Assuming FilterOptionValue can be directly used as string
                filteredValues = filterValues.map(value => String(value));
            } else if (filterValues != null) {
                // Handle a single FilterOptionValue or non-array
                filteredValues = [String(filterValues)];
            }

            // Now filteredValues is guaranteed to be string[], can use it safely
            if (filteredValues.length > 0) {
                let raw = normalizeValues(filteredValues);
                queryParam[key + 'JsonStr'] = JSON.stringify(raw);
                handleSearch();
            } else {
                delete queryParam[key + 'JsonStr'];
                handleSearch();
            }
            columnsMap[key].filterOptionValues = filters[key]
        }
    }

    const anotherHandler = (filters: any) => {
        console.log('Second handler:', filters);
        // 其他处理逻辑
    };

    // 合并这两个处理器到一个事件监听器
    const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
        handleFilters(filters, initiatorColumn);
        anotherHandler(filters);
    };

    const data = ref<FygDataSource[]>(createData());

    function createData(): FygDataSource[] {
        return [
            {
                id: 1,
                transactionDate: *************,
                bankAccountId: 1,
                incomeAmount: 50000,
                expenseAmount: undefined,
                counterpartyAccountId: 2,
                fundDailyDimensionId: 1,
                taxDetail: '税金明细A',
                developmentCostDetail: '开发成本明细A',
                projectId: projectId,
                purposeDetail: '项目建设',
                counterpartyAccountNumber: '**********',
                counterpartyBankName: '中国银行椒江支行',
                annualInterestRate: 3.5,
                dataStatus: 1,
                createdAt: *************,
                updatedAt: *************,
                createdBy: 'Admin',
                updatedBy: 'Admin'
            },
            {
                id: 2,
                transactionDate: *************,
                bankAccountId: 2,
                incomeAmount: 30000,
                expenseAmount: undefined,
                counterpartyAccountId: 3,
                fundDailyDimensionId: 2,
                taxDetail: '税金明细B',
                developmentCostDetail: '开发成本明细B',
                projectId: projectId,
                purposeDetail: '项目维护',
                counterpartyAccountNumber: '**********',
                counterpartyBankName: '兴业银行台州分行',
                annualInterestRate: 4.0,
                dataStatus: 1,
                createdAt: *************,
                updatedAt: *************,
                createdBy: 'Admin',
                updatedBy: 'Admin'
            },
            {
                id: 3,
                transactionDate: *************,
                bankAccountId: 3,
                incomeAmount: undefined,
                expenseAmount: 20000,
                counterpartyAccountId: 4,
                fundDailyDimensionId: 3,
                taxDetail: '税金明细C',
                developmentCostDetail: '开发成本明细C',
                projectId: projectId,
                purposeDetail: '项目支出',
                counterpartyAccountNumber: '**********',
                counterpartyBankName: '农业银行台州支行',
                annualInterestRate: 3.8,
                dataStatus: 1,
                createdAt: *************,
                updatedAt: *************,
                createdBy: 'Admin',
                updatedBy: 'Admin'
            }
        ];
    }


    type FilterData = {
        key: string,
        value: any
    }
    const doFilter = async (val: FilterData) => {
        if (Array.isArray(val.value)) {
            let raw = normalizeValues(val.value);
            queryParam[val.key] = JSON.stringify(raw);
        } else {
            queryParam[val.key] = val.value;
        }
        await handleSearch()
    }

    type Field = {
        key: string,
        data: ExtendedDataTableBaseColumn,
        type: string,
        toDo?: Function,
        optionKey?: keyof typeof optionsMap,
    }

    const fields: Field[] = [
        {
            key: 'transactionDate',
            data: { title: '交易日期', key: 'transactionDate', width: 150, isActivated: true },
            type: 'datePicker',
            toDo: doFilter
        },
        {
            key: 'projectId',
            data: { title: '项目名称', key: 'projectId', width: 150, isActivated: true },
            type: 'text',
        },
        {
            key: 'fundDailyDimensionId',
            data: { title: '资金维度', key: 'fundDailyDimensionId', width: 150, isActivated: true },
            type: 'options',
            optionKey: 'fundDailyDimensionId'
        },
        {
            key: 'bankAccountId',
            data: { title: '银行账户', key: 'bankAccountId', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'incomeAmount',
            data: { title: '收入金额', key: 'incomeAmount', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'expenseAmount',
            data: { title: '支出金额', key: 'expenseAmount', width: 150, isActivated: true },
            type: 'text'
        },
        { 
            key: 'counterpartyName', 
            data: { title: '对方名称', key: 'counterpartyName', width: 150, isActivated: true },
            type: 'text'
         },
        {
            key: 'counterpartyAccountId',
            data: { title: '对方户名', key: 'counterpartyAccountId', width: 150, isActivated: true },
            type: 'text',
        },
        {
            key: 'taxDetail',
            data: { title: '税金明细', key: 'taxDetail', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'developmentCostDetail',
            data: { title: '开发成本明细', key: 'developmentCostDetail', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'purposeDetail',
            data: { title: '详细用途', key: 'purposeDetail', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'counterpartyAccountNumber',
            data: { title: '对方账号', key: 'counterpartyAccountNumber', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'counterpartyBankName',
            data: { title: '对方开户行', key: 'counterpartyBankName', width: 150, isActivated: true },
            type: 'text'
        },
        {
            key: 'annualInterestRate',
            data: { title: '年利率', key: 'annualInterestRate', width: 150, isActivated: true },
            type: 'text'
        },];
    // 定义操作列
    const actions = (): ExtendedDataTableBaseColumn => {
        return {
            title: '操作',
            key: 'actions',
            width: 180,
            render(row, index) {
                return h(NSpace, { size: 'small', inline: true }, {
                    default: () => [
                        h(NButton, {
                            size: 'small',
                            onClick: () => toggleEdit(row, index)
                        }, { default: () => ('编辑') }),
                        h(NButton, {
                            size: 'small',
                            type: 'error',
                            onClick: () => deleteRow(index)
                        }, { default: () => '删除' })
                    ]
                })
            },
            fixed: 'right',
            isActivated: true
        }
    }

    function addRow() {
        router.push({
                path: '/fyg/accounting/datasource/edit/new',
            });
    }

    const goBack = () => {
        router.back(); // 返回上一级页面
    };

    function toggleEdit(row: FygDataSource, index: number) {
        if (row.id) {
            // const encryptId = encryptRowId(row.id);
            router.push({
                path: '/fyg/accounting/datasource/edit/' + row.id,
            });
        } else {
            message.error('项目ID无效，请刷新页面重试！');
        }
    }

    // 删除一行
    function deleteRow(index: number) {
        if (Array.isArray(data.value)) {
            // data.content 是一个数组，可以安全地使用 splice 方法
            data.value.splice(index, 1);
        } else {
            // data.content 不是一个数组，抛出错误或进行其他处理
            console.error('data.content is not an array!');
        }
    }

    // 定义 setColumn 函数，根据 userSetting 动态生成 columns
    const setColumn = (settings: SETTING[]) => {
        let columnsArray: ExtendedDataTableBaseColumn[] = [];
        let columnsMap: any = {};

        settings.forEach((setting) => {
            if (setting.isActivated) {
                let column: any;
                const field = fields.find(f => f.key === setting.key);
                if (field) {
                    if (field.type === 'text') {
                        column = createColumnWithTemplate(field.data, field.type, field.toDo);
                    } else if (field.type === 'options') {
                        if (field.optionKey)
                            column = createColumnWithFilterOptions(field.data, field.optionKey, optionsMap);
                    } else if (field.type === 'datePicker') {
                        column = createColumnWithDatePickerTemplate(field.data, field.type, field.toDo);
                    }
                    // 添加到数组
                    columnsArray.push(column);
                    // 同时添加到映射
                    columnsMap[field.data.key] = column;
                }
            }
        });
        columnsArray.push(actions());
        return { columnsArray, columnsMap };
    }
    // 获取列数据
    const DEFAULT_SETTING: SETTING[] = [
        { key: 'transactionDate', title: '交易日期', isActivated: true },
        { key: 'bankAccountId', title: '银行账户', isActivated: true },
        { key: 'incomeAmount', title: '收入金额', isActivated: true },
        { key: 'expenseAmount', title: '支出金额', isActivated: true },
        { key: 'counterpartyName', title: '对方名称', isActivated: true},
        { key: 'counterpartyAccountId', title: '对方户名', isActivated: true },
        { key: 'fundDailyDimensionId', title: '资金日报维度', isActivated: true },
        { key: 'taxDetail', title: '税金明细', isActivated: true },
        { key: 'developmentCostDetail', title: '开发成本明细', isActivated: true },
        { key: 'purposeDetail', title: '详细用途', isActivated: true },
        { key: 'counterpartyAccountNumber', title: '对方账号', isActivated: true },
        { key: 'counterpartyBankName', title: '对方开户行', isActivated: true },
        { key: 'annualInterestRate', title: '年利率', isActivated: true },
        { key: 'projectId', title: '项目名称', isActivated: true }
    ];
    const userSetting = reactive<SETTING[]>(DEFAULT_SETTING);
    const { columnsArray, columnsMap } = setColumn(userSetting);

    //table
    const loadingRef = ref(true);
    const loading = loadingRef;

    const initialPaginationState = {
        itemCount: 0,
        pageCount: 1,
        page: 1,
        pageSize: 15,
        showSizePicker: true,
        pageSizes: [15, 25, 50],
        onChange: (page: number) => {
            paginationReactive.page = page;
            handleSearch();
        },
        onUpdatePageSize: (pageSize: number) => {
            paginationReactive.pageSize = pageSize
            paginationReactive.page = 1
            handleSearch();
        },
        prefix({ itemCount }: { itemCount?: number }) {
            return `Total is ${itemCount}.`
        }
    }

    const paginationReactive = reactive({ ...initialPaginationState });

    // 定义重置方法
    async function resetPagination() {
        Object.assign(paginationReactive, { ...initialPaginationState });
    }

    //query
    const initialQueryParam: QueryParam = {
        dataStatus: 0,
        currentPage: paginationReactive.page - 1,
        pageSize: paginationReactive.pageSize,
        projectName: undefined,
        columnKey: 'transactionDate',
        projectId: projectId !== 0 ? projectId : undefined,
        order: 'descend'
    };

    const queryParam = reactive<QueryParam>({ ...initialQueryParam });

    async function resetQueryParam() {
        Object.assign(queryParam, { ...initialQueryParam });
    }

    const handleSearch = async () => {
        loadingRef.value = true
        queryParam.pageSize = paginationReactive.pageSize;
        queryParam.currentPage = paginationReactive.page - 1;
        await getData(queryParam);
    }

    //data
    async function getData(queryParam?: QueryParam) {
        // const url = BASE_SERVER + 'filter'
        // const results = await fetchData(url, queryParam);
        // if (results.length > 0) {
        //     const result = results[0];
        //     result.content.forEach((item: Project) => {
        //         Object.keys(item).forEach(key => {
        //             // 仅当项的值不是 null 或 undefined 时才继续处理
        //             if (item[key] != null && typeof item[key] === 'string') {
        //                 const trimmedValue = item[key].trim();
        //                 // 如果字符串不为空，尝试转换为数字
        //                 if (trimmedValue !== '') {
        //                     const convertedNumber = Number(trimmedValue);
        //                     // 只有当转换结果为有效数字时，才更新该值
        //                     if (!isNaN(convertedNumber)) {
        //                         item[key] = convertedNumber;
        //                     }
        //                 }
        //             }
        //         });
        //     });

        //     Object.assign(data, {
        //         ...result  // 确保从服务器返回的结果是完整的，包括所有需要的字段
        //     });
        // }
        // 获取项目数据
        // paginationReactive.pageCount = data.totalPages ? data.totalPages : 0;
        // paginationReactive.itemCount = data.totalElements ? data.totalElements : 0;
        setTimeout(() => {
            loadingRef.value = false
        }, 1000);
    }

    onMounted(() => {
        handleSearch()
    });

</script>