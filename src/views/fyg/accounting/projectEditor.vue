<template>
    <div :style="{
        display: 'flex',
        width: '100%',
        boxSizing: 'border-box',
        padding: '0px',
        backgroundColor: 'var(--color-fill-2)',
    }">
        <n-card title="编辑项目" size="small" :style="cardStyle">
            <div class="custom-display">
                <project-edit ref="sc" :width="myWindow.width" :project-id="projectId" :editor="editor" @update:status="handleStatusChange" />
            </div>
            <div class="fixed-bottom">
                <n-space style="width: 200px;">
                    <n-button type="primary" size="small" @click="handleSubmit" :disabled="isSubmitting">提交</n-button>
                    <n-button type="info" size="small" @click="goBack">返回</n-button>
                </n-space>
            </div>
        </n-card>
    </div>

</template>
<script setup lang="ts">
    import ProjectEdit from '../../../components/fyg/accounting/projects/ProjectEdit.vue';
    import { decryptRowId } from '../../../utils/APIUtils';
    import { useRoute, useRouter } from 'vue-router'
    const route = useRoute();
    const router = useRouter();
    const encryptedId: string = route.params?.id as string;
    const projectId:number = decryptRowId(encryptedId);

    const goBack = () => {
        router.back(); // 返回上一级页面
    };
    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const editor = vueCookies.get('account');
    const sc = ref<any>(null);

    const isSubmitting = ref<boolean>(false);
    const handleSubmit = (e: MouseEvent) => {
        console.log('handleSubmit called'); // Debug log
        sc.value.doSave(e)
    };

    const handleStatusChange = (status: boolean) => {
        console.log(`output->status`,status)
        isSubmitting.value = status;
    };

    //window size
    const Window: any = window
    type MyWindow = {
        width: number,
        height: number
    }
    const myWindow = reactive<MyWindow>({
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
    })

    // card 样式
    type CardStyle = {
        width: string;
    };
    const cardStyle = reactive<CardStyle>({
        width: myWindow.width - 20 + 'px',
    });

    watch(myWindow, (newValue, oldValue) => {
        cardStyle.width = newValue.width - 20 + 'px';
    })


    onMounted(() => {
        Window.onresize = () => {
            return (() => {
                Window.fullHeight = document.documentElement.clientHeight
                Window.fullWidth = document.documentElement.clientWidth
                myWindow.height = Window.fullHeight
                myWindow.width = Window.fullWidth
            })()
        }
    })
</script>
<style scoped>
    .n-card {
        width: 100%;
    }

    .custom-display {
        display: flex;
        justify-content: center;
    }

    .fixed-bottom {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 20px;
        margin: auto;
        width: 100px;
        /* 你可以根据需要设置宽度 */
        text-align: center;
        /* background-color: #f8f9fa; */
        background-color: transparent;
        /* 背景颜色 */
        padding: 4px;
        border-radius: 10px;
        box-shadow: 0 0 0px rgba(0, 0, 0, 0.2);
    }
</style>