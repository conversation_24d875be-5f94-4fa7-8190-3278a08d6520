<template>
    <n-data-table :columns="columns" :data="data" :pagination="pagination" :bordered="false" />
</template>
<script setup lang="ts">
import { h, } from 'vue'
import { NButton } from 'naive-ui'
import type { DataTableColumns } from 'naive-ui'
import { inject } from 'vue';

// 从全局注入中获取 message 实例
const message:any = inject('message');

type Song = {
    no: number
    title: string
    length: string
}

const pagination = false;

const createColumns = ({
    play
}: {
    play: (row: Song) => void
}): DataTableColumns<Song> => {
    return [
        {
            title: 'No',
            key: 'no',
            width: 50
        },
        {
            title: 'Title',
            key: 'title',
            resizable: true
        },
        {
            title: 'Length (minWidth: 100, maxWidth: 500)',
            key: 'length',
            resizable: true,
            minWidth: 100,
            maxWidth: 500
        },
        {
            title: 'Action',
            key: 'actions',
            render(row) {
                return h(
                    NButton,
                    {
                        strong: true,
                        tertiary: true,
                        size: 'small',
                        onClick: () => play(row)
                    },
                    { default: () => 'Play' }
                )
            }
        }
    ]
}

const columns = createColumns({
    play(row: Song) {
        if (message) {
            message.info(`Play ${row.title}`);
        }
    }
})

const data: Song[] = [
    { no: 3, title: 'Wonderwall', length: '4:18' },
    { no: 4, title: "Don't Look Back in Anger", length: '4:48' },
    { no: 12, title: 'Champagne Supernova', length: '7:27' }
]
</script>
<style scoped></style>