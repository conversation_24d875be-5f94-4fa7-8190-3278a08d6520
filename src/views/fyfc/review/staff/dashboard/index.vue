<template>
    <div class="page-wrapper">
        <div class="container">
            <div class="content">
                <n-card title="我的绩效评价" size="small" :bordered="true" class="dashboard-card">
            <!-- 右上角新增按钮 -->
            <template #header-extra>
                <n-button type="primary" @click="handleCreateNew" :loading="loading">
                    <template #icon>
                        <n-icon><AddIcon /></n-icon>
                    </template>
                    新增评价
                </n-button>
            </template>

            <!-- 评价列表 -->
            <n-list v-if="sortedEvaluationList.length > 0" hoverable clickable>
                <n-list-item
                    v-for="evaluation in sortedEvaluationList"
                    :key="evaluation.id"
                    @click="handleItemClick(evaluation)"
                    class="evaluation-item"
                >
                    <template #prefix>
                        <n-avatar
                            round
                            size="medium"
                            :style="{ backgroundColor: getStatusColor(evaluation.status) }"
                        >
                            <n-icon><DocumentIcon /></n-icon>
                        </n-avatar>
                    </template>

                    <n-thing>
                        <template #header>
                            <div class="evaluation-header">
                                <span class="evaluation-title">
                                    {{ formatDate(evaluation.reviewDate) }} 绩效评价
                                </span>
                                <n-tag
                                    :type="getStatusTagType(evaluation.status)"
                                    size="small"
                                    class="status-tag"
                                >
                                    {{ getStatusLabel(evaluation.status as EvaluationStatus) }}
                                </n-tag>
                            </div>
                        </template>

                        <template #description>
                            <div class="evaluation-details">
                                <div class="detail-item">
                                    <n-icon class="detail-icon"><ScoreIcon /></n-icon>
                                    <span class="detail-label">最终得分:</span>
                                    <span class="detail-value score-value">
                                        {{ evaluation.score?.toFixed(1) || '待评分' }} 分
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <n-icon class="detail-icon"><DepartmentIcon /></n-icon>
                                    <span class="detail-label">部门:</span>
                                    <span class="detail-value">{{ evaluation.department || '未填写' }}</span>
                                </div>
                                <div class="detail-item">
                                    <n-icon class="detail-icon"><TimeIcon /></n-icon>
                                    <span class="detail-label">创建时间:</span>
                                    <span class="detail-value">{{ formatDateTime(evaluation.createdAt) }}</span>
                                </div>
                            </div>
                        </template>

                        <template #action>
                            <n-space>
                                <n-button
                                    size="small"
                                    type="primary"
                                    ghost
                                    @click.stop="handleEdit(evaluation)"
                                >
                                    编辑
                                </n-button>
                                <n-button
                                    size="small"
                                    @click.stop="handleView(evaluation)"
                                >
                                    查看
                                </n-button>
                            </n-space>
                        </template>
                    </n-thing>
                </n-list-item>
            </n-list>

            <!-- 空状态 -->
            <n-empty
                v-else
                description="暂无评价记录"
                class="empty-state"
                size="large"
            >
                <template #extra>
                    <n-button type="primary" @click="handleCreateNew">
                        创建第一个评价
                    </n-button>
                </template>
            </n-empty>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <n-spin size="large">
                    <template #description>
                        加载评价列表...
                    </template>
                </n-spin>
            </div>
                </n-card>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import VueCookies from 'vue-cookies';
import {
    NCard,
    NList,
    NListItem,
    NButton,
    NIcon,
    NAvatar,
    NThing,
    NTag,
    NSpace,
    NEmpty,
    NSpin,
    useMessage
} from 'naive-ui';
import {
    Add as AddIcon,
    Document as DocumentIcon,
    Trophy as ScoreIcon,
    Business as DepartmentIcon,
    Time as TimeIcon
} from '@vicons/ionicons5';
import type { Evaluation, EvaluationStatus } from '../../../../../fconfig/fyfc/review';
import { getStatusLabel } from '../../../../../fconfig/fyfc/review';

// 路由和消息
const router = useRouter();
const message = useMessage();

// 获取当前用户信息
const vueCookies: any = VueCookies;
const currentUser = vueCookies.get('account');

// 响应式数据
const loading = ref(false);
const evaluationList = ref<Evaluation[]>([]);

// 模拟数据（实际项目中应该从API获取）
const mockEvaluationData: Evaluation[] = [
    {
        id: 1,
        department: '技术部',
        name: currentUser || 'xlh',
        reviewDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30天前
        score: 85.5,
        additionalScore: 5,
        comment: '表现良好，继续保持',
        status: 'completed',
        createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000,
        createdBy: currentUser || 'xlh',
        updatedBy: 'manager'
    },
    {
        id: 2,
        department: '技术部',
        name: currentUser || 'xlh',
        reviewDate: Date.now() - 60 * 24 * 60 * 60 * 1000, // 60天前
        score: 78.2,
        additionalScore: 3,
        comment: '有进步空间',
        status: 'completed',
        createdAt: Date.now() - 60 * 24 * 60 * 60 * 1000,
        createdBy: currentUser || 'xlh',
        updatedBy: 'manager'
    },
    {
        id: 3,
        department: '技术部',
        name: currentUser || 'xlh',
        reviewDate: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7天前
        score: undefined, // 未完成评分
        additionalScore: undefined,
        comment: '',
        status: 'self',
        createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000,
        createdBy: currentUser || 'xlh',
        updatedBy: currentUser || 'xlh'
    }
];

// 计算属性
const sortedEvaluationList = computed(() => {
    return [...evaluationList.value].sort((a, b) => {
        // 按创建时间倒序排列
        return (b.createdAt || 0) - (a.createdAt || 0);
    });
});

// 方法
const loadEvaluationList = async () => {
    loading.value = true;
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 实际项目中应该调用API
        // const response = await api.getEmployeeEvaluations(currentUser);
        // evaluationList.value = response.data;

        // 使用模拟数据
        evaluationList.value = mockEvaluationData.filter(item => item.name === currentUser);

        message.success('评价列表加载成功');
    } catch (error) {
        console.error('加载评价列表失败:', error);
        message.error('加载评价列表失败');
    } finally {
        loading.value = false;
    }
};

const handleCreateNew = () => {
    // 跳转到新增评价页面，不传递id
    router.push('/fyfc/review/staff/edit');
};

const handleItemClick = (evaluation: Evaluation) => {
    // 点击列表项跳转到编辑页面，传递id
    if (evaluation.id) {
        router.push(`/fyfc/review/staff/edit?id=${evaluation.id}`);
    }
};

const handleEdit = (evaluation: Evaluation) => {
    // 编辑按钮点击
    if (evaluation.id) {
        router.push(`/fyfc/review/staff/edit?id=${evaluation.id}&mode=edit`);
    }
};

const handleView = (evaluation: Evaluation) => {
    // 查看按钮点击
    if (evaluation.id) {
        router.push(`/fyfc/review/staff/edit?id=${evaluation.id}&mode=view`);
    }
};

const formatDate = (timestamp?: number): string => {
    if (!timestamp) return '未设置';
    return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

const formatDateTime = (timestamp?: number): string => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusColor = (status?: string): string => {
    const statusColors: Record<string, string> = {
        'self': '#2080f0',
        'colleague': '#f0a020',
        'manager': '#18a058',
        'completed': '#52c41a'
    };
    return statusColors[status || 'self'] || '#d9d9d9';
};

const getStatusTagType = (status?: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' => {
    const statusTypes: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
        'self': 'primary',
        'colleague': 'warning',
        'manager': 'info',
        'completed': 'success'
    };
    return statusTypes[status || 'self'] || 'default';
};

// 生命周期
onMounted(() => {
    loadEvaluationList();
});
</script>

<style scoped>
/* 最外层包装器 - 重置所有样式 */
.page-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0 !important;
    padding: 0 !important;
    background-color: #f5f5f5;
    overflow-y: auto;
}

/* 容器样式 - 垂直布局居中 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80px;
    box-sizing: border-box;
}

/* 内容区域样式 */
.content {
    width: 1000px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

.dashboard-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

.evaluation-item {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin-bottom: 8px;
}

.evaluation-item:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.evaluation-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

.status-tag {
    flex-shrink: 0;
}

.evaluation-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.detail-icon {
    color: #666;
    font-size: 16px;
}

.detail-label {
    color: #666;
    min-width: 70px;
}

.detail-value {
    color: #333;
    font-weight: 500;
}

.score-value {
    color: #52c41a;
    font-weight: 600;
    font-size: 15px;
}

.empty-state {
    padding: 60px 20px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }

    .evaluation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .evaluation-details {
        gap: 6px;
    }

    .detail-item {
        font-size: 13px;
    }

    .detail-label {
        min-width: 60px;
    }
}

/* 深度样式 */
:deep(.n-card-header) {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

:deep(.n-list-item) {
    padding: 16px;
}

:deep(.n-thing-header) {
    margin-bottom: 8px;
}

:deep(.n-thing-description) {
    margin-bottom: 12px;
}
</style>