<template>
    <div class="page-wrapper">
        <div class="container">
            <div class="content">
                <n-divider />
                <n-card title="方远房地产集团员工月度绩效考核评分表" size="small" :bordered="true">
                </n-card>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
    import { reactive, ref } from 'vue';
    import { Evaluation, UserRole } from '../../../../../fconfig/fyfc/review';
    import { useRoute, useRouter } from 'vue-router';
    import VueCookies from 'vue-cookies';
    const vueCookies: any = VueCookies
    const currentUser = vueCookies.get('account');
    const currentRole = 'employee';
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };

    const evaluation = reactive<Evaluation>({
        name: 'xlh',
    })

    const userRole = ref<UserRole>("unknown");


    function setUserRole(currentUser?: string, currentRole?: UserRole) {
        if (currentRole === "employee") {
            if (!currentUser) {
                if (currentUser === evaluation.name) {
                    userRole.value = "employee";
                } else {
                    userRole.value = "colleague";
                }
            }
        }
    }
</script>
<style scoped>
/* 容器样式 - 绝对居中 */
.container {
    width: 100vw;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 80px;
    background-color: #f5f5f5;
    box-sizing: border-box;
    /* 强制覆盖任何外部样式 */
    position: relative !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
}

/* 内容区域样式 */
.content {
    width: 800px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

/* 分割线样式 */
:deep(.n-divider) {
    width: 100%;
    margin: 0 0 24px 0;
}

/* 卡片样式 */
.n-card {
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

/* 卡片标题居中 */
:deep(.n-card .n-card-header) {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }
}
</style>