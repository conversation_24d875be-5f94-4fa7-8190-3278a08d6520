<template>
    <n-divider />
    <n-h3 style="text-align: center">
        方远房地产集团员工月度绩效考核评分表
    </n-h3>
    <n-card title="自评" size="small" :bordered="false">
    </n-card>
</template>
<script lang="ts" setup>
    import { Evaluation, UserRole } from '../../../../../fconfig/fyfc/review';
    import { useRoute, useRouter } from 'vue-router'
    import VueCookies from 'vue-cookies'
    const vueCookies: any = VueCookies
    const currentUser = vueCookies.get('account');
    const currentRole = 'employee';
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };

    const evaluation = reactive<Evaluation>({
        name: 'xlh',
    })

    const userRole = ref<UserRole>("unknown");


    function setUserRole(currentUser?: string, currentRole?: UserRole) {
        if (currentRole === "employee") {
            if (!currentUser) {
                if (currentUser === evaluation.name) {
                    userRole.value = "employee";
                } else {
                    userRole.value = "colleague";
                }
            }
        }
    }
</script>
<style scoped>
    .n-card {
        width: 100%;
    }
</style>