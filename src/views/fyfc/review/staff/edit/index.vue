<template>
    <div class="container">
        <div class="content">
            <n-divider />
            <n-card title="方远房地产集团员工月度绩效考核评分表" size="small" :bordered="true">
            </n-card>
        </div>
    </div>
</template>
<script lang="ts" setup>
    import { reactive, ref } from 'vue';
    import { Evaluation, UserRole } from '../../../../../fconfig/fyfc/review';
    import { useRoute, useRouter } from 'vue-router';
    import VueCookies from 'vue-cookies';
    const vueCookies: any = VueCookies
    const currentUser = vueCookies.get('account');
    const currentRole = 'employee';
    const route = useRoute();
    const router = useRouter();
    const goBack = () => {
        router.back(); // 返回上一级页面
    };

    const evaluation = reactive<Evaluation>({
        name: 'xlh',
    })

    const userRole = ref<UserRole>("unknown");


    function setUserRole(currentUser?: string, currentRole?: UserRole) {
        if (currentRole === "employee") {
            if (!currentUser) {
                if (currentUser === evaluation.name) {
                    userRole.value = "employee";
                } else {
                    userRole.value = "colleague";
                }
            }
        }
    }
</script>
<style scoped>
/* 容器样式 - 全屏高度，置顶居中 */
.container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 80px 20px 20px 20px; /* 上 右 下 左 */
    box-sizing: border-box;
    background-color: #f5f5f5; /* 添加背景色便于查看效果 */
}

/* 内容区域样式 */
.content {
    width: 100%;
    max-width: 800px; /* 减小最大宽度，更容易看出居中效果 */
    display: flex;
    flex-direction: column;
    align-items: center; /* 内容区域内部也居中 */
}

/* 分割线样式 */
:deep(.n-divider) {
    width: 100%;
    margin: 0 0 24px 0;
}

/* 卡片样式 */
.n-card {
    width: 100%;
    max-width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 40px 16px 16px 16px;
    }

    .content {
        max-width: 100%;
    }
}
</style>