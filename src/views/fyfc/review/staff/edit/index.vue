<template>
    <div class="page-wrapper">
        <div class="container">
            <div class="content">
                <EvaluationEditor
                    v-model="evaluationData"
                    :user-role="currentUserRole"
                    @save="handleSave"
                    @reset="handleReset"
                    @preview="handlePreview"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import VueCookies from 'vue-cookies';
import EvaluationEditor, { type EvaluationData, type UserRoleConfig } from '../../../../../components/fyfc/review/EvaluationEditor.vue';
import type { UserRole } from '../../../../../fconfig/fyfc/review';

// 获取当前用户信息
const vueCookies: any = VueCookies;
const currentUser = vueCookies.get('account');
const systemUserRole = vueCookies.get('userRole') as UserRole || 'employee'; // 从cookies获取系统角色

// 评价数据
const evaluationData = ref<EvaluationData>({
    department: '',
    name: 'xlh',
    reviewDate: Date.now(),
    evaluationTypes: [],
    scores: {
        performanceScore: null,
        attitudeScore: null,
        abilityScore: null,
        growthScore: null
    },
    comment: ''
});

// 动态计算当前用户角色
const currentUserRole = computed<UserRoleConfig>(() => {
    let roleType: UserRole = 'unknown';
    let canEdit = false;

    if (systemUserRole === 'employee') {
        // 如果系统角色是employee，判断是否为本人
        if (currentUser === evaluationData.value.name) {
            roleType = 'employee'; // 本人，可以自评
            canEdit = true;
        } else {
            roleType = 'colleague'; // 他人，同事评价
            canEdit = true;
        }
    } else if (systemUserRole === 'manager') {
        roleType = 'manager'; // 主管，可以审核
        canEdit = true;
    } else if (systemUserRole === 'admin') {
        roleType = 'admin'; // 管理员，可以查看和编辑
        canEdit = true;
    }

    return {
        type: roleType,
        canEdit,
        canView: true
    };
});

// 事件处理器
const handleSave = (data: EvaluationData) => {
    console.log('保存评价数据:', data);
    // 这里可以调用 API 保存数据
    // await saveEvaluation(data);
};

const handleReset = () => {
    console.log('重置表单');
    evaluationData.value = {
        department: '',
        name: currentUser || '',
        reviewDate: Date.now(),
        evaluationTypes: [],
        scores: {
            performanceScore: null,
            attitudeScore: null,
            abilityScore: null,
            growthScore: null
        },
        comment: ''
    };
};

const handlePreview = (data: EvaluationData) => {
    console.log('预览数据:', data);
    // 这里可以打开预览窗口或跳转到预览页面
};
</script>
<style scoped>
/* 最外层包装器 - 重置所有样式 */
.page-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0 !important;
    padding: 0 !important;
    background-color: #f5f5f5;
    overflow-y: auto;
}

/* 容器样式 - 绝对居中 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 80px;
    box-sizing: border-box;
}

/* 内容区域样式 */
.content {
    width: 800px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

/* 分割线样式 */
:deep(.n-divider) {
    width: 100%;
    margin: 0 0 24px 0;
}

/* 卡片样式 */
.n-card {
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

/* 卡片标题居中 */
:deep(.n-card .n-card-header) {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }
}
</style>