<template>
    <div class="page-wrapper">
        <div class="container">
            <!-- 临时角色切换器 -->
            <div class="temp-role-switcher">
                <n-text strong style="margin-right: 12px;">临时角色切换 (测试用):</n-text>
                <n-radio-group v-model:value="tempUserRole" @update:value="handleRoleChange">
                    <n-radio value="employee">员工</n-radio>
                    <n-radio value="colleague">同事</n-radio>
                    <n-radio value="manager">主管</n-radio>
                    <n-radio value="admin">管理员</n-radio>
                </n-radio-group>
            </div>

            <div class="content">
                <EvaluationEditor
                    v-model="evaluationData"
                    :user-role="currentUserRole"
                    @save="handleSave"
                    @reset="handleReset"
                    @preview="handlePreview"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import VueCookies from 'vue-cookies';
import { NText, NRadio, NRadioGroup } from 'naive-ui';
import EvaluationEditor, { type EvaluationData, type UserRoleConfig } from '../../../../../components/fyfc/review/EvaluationEditor.vue';
import type { UserRole } from '../../../../../fconfig/fyfc/review';

// 获取当前用户信息
const vueCookies: any = VueCookies;
const currentUser = vueCookies.get('account');
const systemUserRole = vueCookies.get('userRole') as UserRole || 'colleague'; // 从cookies获取系统角色

// 临时角色切换（用于测试）
const tempUserRole = ref<UserRole>(systemUserRole);

// 临时角色切换方法
const handleRoleChange = (role: UserRole) => {
    tempUserRole.value = role;
};

// 当前用户角色配置
const currentUserRole = computed<UserRoleConfig>(() => {
    return {
        type: tempUserRole.value,
        canEdit: true,
        canView: true
    };
});

// 评价数据
const evaluationData = ref<EvaluationData>({
    department: '技术部', // 添加测试数据
    name: 'xlh',
    reviewDate: Date.now(),
    evaluationTypes: [],
    scores: [
        {
            type: 'employee',
            performanceScore: 50, // 0-60分
            attitudeScore: 8,     // 0-10分
            abilityScore: 9,      // 0-10分
            growthScore: 7        // 0-10分
        },
        {
            type: 'colleague',
            performanceScore: 45, // 0-60分
            attitudeScore: 9,     // 0-10分
            abilityScore: 8,      // 0-10分
            growthScore: 8        // 0-10分
        },
        {
            type: 'manager',
            performanceScore: 55, // 0-60分
            attitudeScore: 9,     // 0-10分
            abilityScore: 9,      // 0-10分
            growthScore: 8        // 0-10分
        }
    ],
    comment: '这是一个测试备注',
    additionalScore: 5        // 0-10分，线上转发
});


// 动态计算当前用户角色
// const currentUserRole = computed<UserRoleConfig>(() => {
//     let roleType: UserRole = 'unknown';
//     let canEdit = false;

//     if (systemUserRole === 'employee') {
//         // 如果系统角色是employee，判断是否为本人
//         if (currentUser === evaluationData.value.name) {
//             roleType = 'employee'; // 本人，可以自评
//             canEdit = true;
//         } else {
//             roleType = 'colleague'; // 他人，同事评价
//             canEdit = true;
//         }
//     } else if (systemUserRole === 'manager') {
//         roleType = 'manager'; // 主管，可以审核
//         canEdit = true;
//     } else if (systemUserRole === 'admin') {
//         roleType = 'admin'; // 管理员，可以查看和编辑
//         canEdit = true;
//     }

//     return {
//         type: roleType,
//         canEdit,
//         canView: true
//     };
// });

// 事件处理器
const handleSave = (data: EvaluationData) => {
    console.log('保存评价数据:', data);
    // 这里可以调用 API 保存数据
    // await saveEvaluation(data);
};

const handleReset = () => {
    console.log('重置表单');
    evaluationData.value = {
        department: '',
        name: currentUser || '',
        reviewDate: Date.now(),
        evaluationTypes: [],
        scores: [],
        comment: '',
        additionalScore: undefined
    };
};

const handlePreview = (data: EvaluationData) => {
    console.log('预览数据:', data);
    // 这里可以打开预览窗口或跳转到预览页面
};
</script>
<style scoped>
/* 最外层包装器 - 重置所有样式 */
.page-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0 !important;
    padding: 0 !important;
    background-color: #f5f5f5;
    overflow-y: auto;
}

/* 容器样式 - 垂直布局居中 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80px;
    box-sizing: border-box;
}

/* 临时角色切换器样式 */
.temp-role-switcher {
    width: 800px;
    max-width: 90vw;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px dashed #2196f3;
    border-radius: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

/* 内容区域样式 */
.content {
    width: 800px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

/* 分割线样式 */
:deep(.n-divider) {
    width: 100%;
    margin: 0 0 24px 0;
}

/* 卡片样式 */
.n-card {
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

/* 卡片标题居中 */
:deep(.n-card .n-card-header) {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }
}
</style>