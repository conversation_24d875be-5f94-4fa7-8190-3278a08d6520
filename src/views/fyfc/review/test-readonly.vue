<template>
  <div class="test-container">
    <h2>测试只读显示</h2>

    <div class="test-section">
      <h3>员工角色 (可编辑)</h3>
      <EvaluationEditor
        v-model="testData"
        :user-role="{ type: 'employee', canEdit: true, canView: true }"
      />
    </div>

    <div class="test-section">
      <h3>同事角色 (只读基本信息)</h3>
      <EvaluationEditor
        v-model="testData"
        :user-role="{ type: 'colleague', canEdit: true, canView: true }"
      />
    </div>

    <div class="test-section">
      <h3>主管角色 (只读基本信息)</h3>
      <EvaluationEditor
        v-model="testData"
        :user-role="{ type: 'manager', canEdit: true, canView: true }"
      />
    </div>

    <div class="test-section">
      <h3>管理员角色 (只读基本信息)</h3>
      <EvaluationEditor
        v-model="testData"
        :user-role="{ type: 'admin', canEdit: true, canView: true }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import EvaluationEditor, { type EvaluationData } from '../../../components/fyfc/review/EvaluationEditor.vue';

const testData = ref<EvaluationData>({
  department: '技术部',
  name: '张三',
  reviewDate: Date.now(),
  evaluationTypes: ['self'],
  scores: {
    performanceScore: 45, // 0-60分
    attitudeScore: 8,     // 0-10分
    abilityScore: 9,      // 0-10分
    growthScore: 7        // 0-10分
  },
  comment: '这是一个测试备注，用于验证只读显示功能是否正常工作。'
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fff;
}

.test-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}
</style>
