<template>
    <n-layout embedded
        :content-style="`height: ${windowHeight}px; padding: 80px; display: flex; justify-content: center; background-color: #1d2748`">
        <n-card :bordered="false" style="border-radius: 15px;"
            :content-style="`margin-top: ${windowHeight / 15}px`"
            :style="`height: ${windowHeight / 2.5 + windowHeight / 5}px`">
            <template #cover>
                <div style="background-color:#1d2748;">
                    <img :src="logo" alt="Logo">
                </div>
            </template>

            <!-- 密码登录表单 -->
            <n-form v-if="!isCodeLogin" ref="formRef" :model="loginForm" :rules="validationRules"
                label-placement="left" label-width="auto">
                <n-form-item path="account" label="用户名">
                    <n-input
                        v-model:value="loginForm.account"
                        placeholder="请输入用户名"
                        :input-props="{ autocomplete: 'username' }"
                        @keydown.enter.prevent="handleLogin" />
                </n-form-item>
                <n-form-item path="password" label="密码">
                    <n-input
                        v-model:value="loginForm.password"
                        type="password"
                        placeholder="请输入密码"
                        :input-props="{ autocomplete: 'current-password' }"
                        show-password-on="click"
                        @input="handlePasswordInput"
                        @keydown.enter.prevent="handleLogin" />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <div class="button-container">
                            <n-button-group>
                                <n-button
                                    round
                                    :style="buttonStyle.login"
                                    :disabled="!loginForm.account || isLoading"
                                    :loading="isLoading"
                                    color="#3066f2"
                                    @click="handleLogin">
                                    <template #icon>
                                        <n-icon><LogInIcon /></n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button
                                    round
                                    :style="buttonStyle.switch"
                                    @click="toggleLoginMode">
                                    <template #icon>
                                        <n-icon><RepeatIcon /></n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>

            <!-- 验证码登录表单 -->
            <n-form v-else ref="formRef" :model="loginForm" :rules="validationRules"
                label-placement="left" label-width="auto">
                <n-form-item path="account" label="手机号">
                    <n-input
                        v-model:value="loginForm.account"
                        placeholder="请输入手机号"
                        :input-props="{ autocomplete: 'tel' }"
                        @keydown.enter.prevent="handleLogin" />
                </n-form-item>
                <n-form-item path="password" label="验证码">
                    <n-input
                        v-model:value="loginForm.password"
                        placeholder="请输入验证码"
                        :input-props="{ autocomplete: 'one-time-code' }"
                        @input="handlePasswordInput"
                        @keydown.enter.prevent="handleLogin" />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <div class="button-container">
                            <n-button-group>
                                <n-button
                                    round
                                    :style="buttonStyle.loginSms"
                                    :disabled="!loginForm.account || isLoading"
                                    :loading="isLoading"
                                    color="#3066f2"
                                    @click="handleLogin">
                                    <template #icon>
                                        <n-icon><LogInIcon /></n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button
                                    round
                                    :style="buttonStyle.getCode"
                                    :disabled="!loginForm.account || countdown < COUNTDOWN_DURATION"
                                    :loading="isSendingCode"
                                    @click="handleGetVerifyCode">
                                    <template #icon>
                                        <n-icon><ChatboxIcon /></n-icon>
                                    </template>
                                    {{ countdown < COUNTDOWN_DURATION ? `${countdown}秒` : '获取验证码' }}
                                </n-button>
                                <n-button
                                    round
                                    :style="buttonStyle.switchSms"
                                    @click="toggleLoginMode">
                                    <template #icon>
                                        <n-icon><RepeatIcon /></n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>
        </n-card>
    </n-layout>
</template>
<script setup lang="ts">
import logo from '../../assets/fyg/logo.png';
import { decode } from 'js-base64';
import { doPost } from "../../utils/AxiosUtil";
import { onMounted, ref, watch, onUnmounted, inject, computed } from "vue";
import { useRouter } from "vue-router";
import VueCookies from 'vue-cookies';
import { LogInOutline as LogInIcon, Repeat as RepeatIcon, ChatboxOutline as ChatboxIcon } from '@vicons/ionicons5';
import {
  NLayout,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NRow,
  NCol,
  NButton,
  NButtonGroup,
  NIcon,
  type FormInst,
  type FormItemRule
} from 'naive-ui';

// 常量定义
const API_ENDPOINTS = {
  SMS_CODE: '/aliapi/sms/authCode?appcode=d6388b546ee444b2888dc2bcb94f1eb9',
  LOGIN_PASSWORD: '/aliapi/auth/jwt?appcode=d6388b546ee444b2888dc2bcb94f1eb9',
  LOGIN_SMS: '/aliapi/auth/jwt/sms?appcode=d6388b546ee444b2888dc2bcb94f1eb9'
} as const;

const VALIDATION_PATTERNS = {
  MOBILE: /^1[3-9]\d{9}$/,
  SMS_CODE: /^\d{4}$/
} as const;

const COUNTDOWN_DURATION = 60;
const SUCCESS_CODE = '00000';

// 从全局注入中获取 message 实例
const message: any = inject('message');

// 类型定义
interface LoginModel {
  account: string | null;
  password: string | null;
}

interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
}

interface JWTPayload {
  sub: string;
  userId: string;
  authorities: string[];
  exp: number;
  iat: number;
  jti: string;
  username: string;
  easId?: string;
}

interface LoginParams {
  username?: string;
  password?: string;
  mobile?: string;
  code?: string;
}

// 路由和状态管理
const { push } = useRouter();
const historyPath = VueCookies.get('historyPath') as string | null;

// 响应式状态
const countdown = ref<number>(COUNTDOWN_DURATION);
const isCodeLogin = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const isSendingCode = ref<boolean>(false);

// 切换登录方式
const toggleLoginMode = (): void => {
  isCodeLogin.value = !isCodeLogin.value;
  // 切换登录方式时重置倒计时和表单
  countdown.value = COUNTDOWN_DURATION;
  loginForm.value.account = null;
  loginForm.value.password = null;
  // 清理定时器
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
};

// 处理获取验证码按钮点击
const handleGetVerifyCode = (): void => {
  // 手动验证手机号
  if (!loginForm.value.account) {
    message.error("请输入手机号");
    return;
  }

  // 验证手机号格式
  if (!VALIDATION_PATTERNS.MOBILE.test(loginForm.value.account)) {
    message.error("请输入正确的手机号");
    return;
  }

  isSendingCode.value = true;
  // 开始倒计时
  countdown.value = COUNTDOWN_DURATION - 1;
  countdownTimer();

  // 这里添加获取验证码的API调用
  doPost<ApiResponse>(API_ENDPOINTS.SMS_CODE, { mobile: loginForm.value.account }).then((res) => {
    if (res.code === SUCCESS_CODE) {
      message.success("验证码已发送");
    } else {
      message.error(res.msg);
      // 如果API调用失败，重置倒计时
      countdown.value = COUNTDOWN_DURATION;
      if (timerId !== null) {
        clearTimeout(timerId);
        timerId = null;
      }
    }
  }).catch((err: any) => {
    message.error(err);
    // 如果API调用出错，重置倒计时
    countdown.value = COUNTDOWN_DURATION;
    if (timerId !== null) {
      clearTimeout(timerId);
      timerId = null;
    }
  }).finally(() => {
    isSendingCode.value = false;
  });
};

// 倒计时定时器ID
let timerId: number | null = null;

// 优化倒计时函数
const countdownTimer = (): void => {
  if (countdown.value > 0) {
    timerId = window.setTimeout(() => {
      countdown.value--;
      countdownTimer();
    }, 1000);
  } else {
    countdown.value = 60;
  }
};

// 表单引用
const formRef = ref<FormInst | null>(null);

// 表单数据模型
const loginForm = ref<LoginModel>({
  account: null,
  password: null,
});

// 表单验证规则
const validationRules = computed(() => ({
  account: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          return new Error(isCodeLogin.value ? "请输入手机号" : "请输入账号");
        }
        if (isCodeLogin.value && !VALIDATION_PATTERNS.MOBILE.test(value)) {
          return new Error("请输入正确的手机号");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
  password: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          return new Error(isCodeLogin.value ? "请输入验证码" : "请输入密码");
        }
        if (isCodeLogin.value && !VALIDATION_PATTERNS.SMS_CODE.test(value)) {
          return new Error("请输入4位数字验证码");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
}));

// 按钮样式计算属性
const buttonStyle = computed(() => {
  const baseWidth = windowWidth.value / 2.66;
  return {
    login: `width: ${baseWidth < 200 ? baseWidth : 160}px`,
    switch: `width: ${baseWidth < 200 ? baseWidth : 160}px`,
    loginSms: `width: ${baseWidth < 100 ? baseWidth : 80}px`,
    getCode: `width: ${baseWidth < 150 ? baseWidth : 120}px`,
    switchSms: `width: ${baseWidth < 160 ? baseWidth : 140}px`
  };
});

// 密码输入处理
const handlePasswordInput = (): void => {
  // 这个函数在当前登录表单中不需要重新输入密码验证
  // 保留空实现以防后续需要
};

// 登录按钮点击处理
const handleLogin = async (): Promise<void> => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
  } catch (errors) {
    console.log('Validation errors:', errors);
    message.error("请检查输入信息");
    return;
  }

  isLoading.value = true;

  const url = isCodeLogin.value ? API_ENDPOINTS.LOGIN_SMS : API_ENDPOINTS.LOGIN_PASSWORD;
  const param: LoginParams = isCodeLogin.value
    ? {
        mobile: loginForm.value.account || undefined,
        code: loginForm.value.password || undefined
      }
    : {
        username: loginForm.value.account || undefined,
        password: loginForm.value.password || undefined
      };

  try {
    const res = await doPost<ApiResponse>(url, param);

    if (res.code === SUCCESS_CODE) {
      message.success("登录成功");
      const jsonData = weappJwtDecode(res.data, {});
      const timestamp = Date.now() / 1000;
      console.info('JWT data:', jsonData);
      const expirationDays = (jsonData.exp - timestamp) / (60 * 60 * 24);

      VueCookies.set('easId', jsonData.easId || '', `${expirationDays}d`);
      VueCookies.set('account', param.username || param.mobile || '', `${expirationDays}d`);
      VueCookies.set('x-token', res.data, `${expirationDays}d`);

      push(historyPath || '/');
    } else {
      message.error(res.msg || '登录失败');
    }
  } catch (error) {
    console.error('Login error:', error);
    message.error('登录失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};
// 工具函数（保留以备后用）
// const getJsonObj = (str: string): any => {
//   const jsonData = JSON.parse(str);
//   return jsonData;
// };

// const getJWTStr = (str: string): string => {
//   const firstIndex = str.indexOf('}') + 1;
//   const secondIndex = str.indexOf('}', firstIndex) + 1;
//   return str.slice(firstIndex, secondIndex);
// };

// JWT 解码函数
function weappJwtDecode(token: string, options: { header?: boolean } = {}): JWTPayload {
  if (typeof token !== "string") {
    throw new Error("Invalid token specified");
  }

  const pos = options.header === true ? 0 : 1;
  try {
    return JSON.parse(decode(token.split(".")[pos]));
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'Unknown error';
    throw new Error("Invalid token specified: " + errorMessage);
  }
}
// 窗口尺寸响应式状态
const windowWidth = ref<number>(document.documentElement.clientWidth);
const windowHeight = ref<number>(document.documentElement.clientHeight);

// 监听窗口尺寸变化
watch(windowHeight, () => {
  // console.info("实时屏幕高度：", newValue)
});

watch(windowWidth, () => {
  // console.info("实时屏幕宽度：", newValue)
});

// 生命周期钩子
onMounted(() => {
  // 窗口大小变化监听
  window.onresize = () => {
    const newHeight = document.documentElement.clientHeight;
    const newWidth = document.documentElement.clientWidth;
    windowHeight.value = newHeight;
    windowWidth.value = newWidth;
  };

  // 测试 JWT 解码（开发环境）
  const testStr = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  console.info('decode', weappJwtDecode(testStr, {}));
});

// 组件卸载时清理
onUnmounted(() => {
  if (timerId !== null) {
    clearTimeout(timerId);
  }
});

</script>
<style scoped>
.n-card {
  max-width: 400px;
}

.button-container {
  display: flex;
  justify-content: center;
}

/* 优化移动端显示 */
@media (max-width: 768px) {
  .n-card {
    max-width: 90vw;
    margin: 0 auto;
  }
}

/* 密码输入框样式优化 */
:deep(.n-input--password .n-input__eye) {
  cursor: pointer;
}

/* 按钮组样式优化 */
:deep(.n-button-group .n-button) {
  margin: 0 2px;
}

/* 表单项间距优化 */
:deep(.n-form-item) {
  margin-bottom: 16px;
}
</style>