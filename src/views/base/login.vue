<template>
    <n-layout embedded
        :content-style="'height: ' + windowHeight + 'px;padding: 80px;display: flex;justify-content: center;background-color: #1d2748'">
        <n-card :bordered="false" style="border-radius: 15px;"
            :content-style="'margin-top:' + windowHeight / 15 + 'px;'"
            :style="'height:' + (windowHeight / 2.5 + windowHeight / 5) + 'px'">
            <template #cover>
                <div style="background-color:#1d2748;">
                    <img :src="logo">
                </div>
            </template>
            <n-form v-if="!ifSwitch" ref="formRef" :model="model" :rules="rules" label-placement="left"
                label-width="auto">
                <n-form-item path="account" label="用户名">
                    <n-input v-model:value="model.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="密码">
                    <n-input v-model:value="model.password" type="password" @input="handlePasswordInput"
                        @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <!-- <div style="display: flex; justify-content: flex-end"> -->
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    :disabled="model.account === null" color="#3066f2"
                                    @click="handleValidateButtonClick">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    @click="handleSwitch">
                                    <template #icon>
                                        <n-icon>
                                            <RepeatIcon />
                                        </n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>

            <n-form v-else ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="auto">
                <n-form-item path="account" label="手机号">
                    <n-input v-model:value="model.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="验证码">
                    <n-input v-model:value="model.password" @input="handlePasswordInput" @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <!-- <div style="display: flex; justify-content: flex-end"> -->
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 100 ? windowWidth / 2.66 : 80) + 'px;'"
                                    :disabled="model.account === null" color="#3066f2"
                                    @click="handleValidateButtonClick">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 150 ? windowWidth / 2.66 : 120) + 'px;'"
                                    :disabled="model.account === null || countdown < 60" @click="handleGetVerifyCode">
                                    <template #icon>
                                        <n-icon>
                                            <ChatboxIcon />
                                        </n-icon>
                                    </template>
                                    {{ countdown < 60 ? `${countdown}秒` : '获取验证码' }} </n-button>
                                        <n-button round
                                            :style="'width:' + (windowWidth / 2.66 < 160 ? windowWidth / 2.66 : 140) + 'px;'"
                                            @click="handleSwitch">
                                            <template #icon>
                                                <n-icon>
                                                    <RepeatIcon />
                                                </n-icon>
                                            </template>
                                            切换登录方式
                                        </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>
        </n-card>
    </n-layout>
</template>
<script setup lang="ts">
  // 从全局注入中获取 message 实例
const message: any = inject('message');
import logo from '../../assets/fyg/logo.png';
import { decode } from 'js-base64';
import { doPost } from "../../utils/AxiosUtil";
import { onMounted, ref, watch, onUnmounted, inject } from "vue";
import { useRouter } from "vue-router";
import VueCookies from 'vue-cookies';
import { LogInOutline as LogInIcon, Repeat as RepeatIcon, ChatboxOutline as ChatboxIcon } from '@vicons/ionicons5';
import {
  NLayout,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NRow,
  NCol,
  NButton,
  NButtonGroup,
  NIcon,
  type FormInst,
  type FormValidationError,
  type FormItemRule
} from 'naive-ui';

// 类型定义
interface LoginModel {
  account: string | null;
  password: string | null;
}

interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
}

interface JWTPayload {
  sub: string;
  userId: string;
  authorities: string[];
  exp: number;
  iat: number;
  jti: string;
  username: string;
  easId?: string;
}

interface LoginParams {
  username?: string;
  password?: string;
  mobile?: string;
  code?: string;
}

// 路由和状态管理
const { push } = useRouter();
const historyPath = VueCookies.get('historyPath') as string | null;

// 响应式状态
const countdown = ref<number>(60);
const ifSwitch = ref<boolean>(false);

// 切换登录方式
const handleSwitch = (): void => {
  ifSwitch.value = !ifSwitch.value;
  // 切换登录方式时重置倒计时
  countdown.value = 60;
};

// 处理获取验证码按钮点击
const handleGetVerifyCode = (): void => {
  // 手动验证手机号
  if (!model.value.account) {
    message.error("请输入手机号");
    return;
  }

  // 验证手机号格式
  if (!/^1[3-9]\d{9}$/.test(model.value.account)) {
    message.error("请输入正确的手机号");
    return;
  }

  // 开始倒计时
  countdown.value = 59;
  countdownTimer();

  // 这里添加获取验证码的API调用
  const url = '/aliapi/sms/authCode?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
  doPost<ApiResponse>(url, { mobile: model.value.account }).then((res) => {
    if (res.code == '00000') {
      message.success("验证码已发送");
    } else {
      message.error(res.msg);
      // 如果API调用失败，重置倒计时
      countdown.value = 60;
    }
  }).catch((err: any) => {
    message.error(err);
    // 如果API调用出错，重置倒计时
    countdown.value = 60;
  });
};

// 倒计时定时器ID
let timerId: number | null = null;

// 优化倒计时函数
const countdownTimer = (): void => {
  if (countdown.value > 0) {
    timerId = window.setTimeout(() => {
      countdown.value--;
      countdownTimer();
    }, 1000);
  } else {
    countdown.value = 60;
  }
};

// 表单引用
const formRef = ref<FormInst | null>(null);

// 表单数据模型
const modelRef = ref<LoginModel>({
  account: null,
  password: null,
});

const model = modelRef;

// 表单验证规则
const rules = {
  account: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          if (ifSwitch.value) {
            if (!/^1[3-9]\d{11}$/.test(value)) {
              return new Error("请输入正确的手机号");
            }
          }
          return new Error("请输入账号");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
  password: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          if (ifSwitch.value) {
            if (!/^\d{4}$/.test(value)) {
              return new Error("请输入4位数字验证码");
            }
          }
          return new Error("请输入密码");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
};

// 密码输入处理
const handlePasswordInput = (): void => {
  // 这个函数在当前登录表单中不需要重新输入密码验证
  // 保留空实现以防后续需要
};

// 登录按钮点击处理
const handleValidateButtonClick = (e: Event): void => {
  e.preventDefault();
  formRef.value?.validate((errors: Array<FormValidationError> | undefined) => {
    if (!errors) {
      let url = '/aliapi/auth/jwt?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
      let param: LoginParams = {};

      if (ifSwitch.value) {
        // 处理验证码登录逻辑
        url = '/aliapi/auth/jwt/sms?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
        param = {
          mobile: model.value.account || undefined,
          code: model.value.password || undefined
        };
      } else {
        // 处理密码登录逻辑
        param = {
          username: model.value.account || undefined,
          password: model.value.password || undefined
        };
      }

      doPost<ApiResponse>(url, param).then((res) => {
        if (res.code == '00000') {
          message.success("验证成功");
          const jsonData = weappJwtDecode(res.data, {});
          const date = new Date();
          const timestamp = date.getTime() / 1000;
          console.info('json', jsonData);
          const d = jsonData.exp - timestamp;
          const day = d / 60 / 60 / 24;

          VueCookies.set('easId', jsonData.easId || '', day + 'd');
          VueCookies.set('account', param.username || param.mobile || '', day + 'd');
          VueCookies.set('x-token', res.data, day + 'd');

          push(historyPath || '/');
        } else {
          message.error(res.msg);
        }
      }).catch((err: any) => {
        console.info('err', err);
        message.error(err);
      });
    } else {
      console.log(errors);
      message.error("验证失败");
    }
  });
};
// 工具函数（保留以备后用）
// const getJsonObj = (str: string): any => {
//   const jsonData = JSON.parse(str);
//   return jsonData;
// };

// const getJWTStr = (str: string): string => {
//   const firstIndex = str.indexOf('}') + 1;
//   const secondIndex = str.indexOf('}', firstIndex) + 1;
//   return str.slice(firstIndex, secondIndex);
// };

// JWT 解码函数
function weappJwtDecode(token: string, options: { header?: boolean } = {}): JWTPayload {
  if (typeof token !== "string") {
    throw new Error("Invalid token specified");
  }

  const pos = options.header === true ? 0 : 1;
  try {
    return JSON.parse(decode(token.split(".")[pos]));
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'Unknown error';
    throw new Error("Invalid token specified: " + errorMessage);
  }
}
// 窗口尺寸响应式状态
const windowWidth = ref<number>(document.documentElement.clientWidth);
const windowHeight = ref<number>(document.documentElement.clientHeight);

// 监听窗口尺寸变化
watch(windowHeight, () => {
  // console.info("实时屏幕高度：", newValue)
});

watch(windowWidth, () => {
  // console.info("实时屏幕宽度：", newValue)
});

// 生命周期钩子
onMounted(() => {
  // 窗口大小变化监听
  window.onresize = () => {
    const newHeight = document.documentElement.clientHeight;
    const newWidth = document.documentElement.clientWidth;
    windowHeight.value = newHeight;
    windowWidth.value = newWidth;
  };

  // 测试 JWT 解码（开发环境）
  const testStr = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
  console.info('decode', weappJwtDecode(testStr, {}));
});

// 组件卸载时清理
onUnmounted(() => {
  if (timerId !== null) {
    clearTimeout(timerId);
  }
});

</script>
<style scoped>
    .n-card {
        max-width: 400px;
    }
</style>