<template>
    <n-layout embedded
        :content-style="'height: ' + windowHeight + 'px;padding: 80px;display: flex;justify-content: center;background-color: #1d2748'">
        <n-card :bordered="false" style="border-radius: 15px;"
            :content-style="'margin-top:' + windowHeight / 15 + 'px;'"
            :style="'height:' + (windowHeight / 2.5 + windowHeight / 5) + 'px'">
            <template #cover>
                <div style="background-color:#1d2748;">
                    <img :src="logo">
                </div>
            </template>
            <n-form v-if="!ifSwitch" ref="formRef" :model="model" :rules="rules" label-placement="left"
                label-width="auto">
                <n-form-item path="account" label="用户名">
                    <n-input v-model:value="model.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="密码">
                    <n-input v-model:value="model.password" type="password" @input="handlePasswordInput"
                        @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <!-- <div style="display: flex; justify-content: flex-end"> -->
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    :disabled="model.account === null" color="#3066f2"
                                    @click="handleValidateButtonClick">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    @click="handleSwitch">
                                    <template #icon>
                                        <n-icon>
                                            <RepeatIcon />
                                        </n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>

            <n-form v-else ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="auto">
                <n-form-item path="account" label="手机号">
                    <n-input v-model:value="model.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="验证码">
                    <n-input v-model:value="model.password" @input="handlePasswordInput" @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <!-- <div style="display: flex; justify-content: flex-end"> -->
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 100 ? windowWidth / 2.66 : 80) + 'px;'"
                                    :disabled="model.account === null" color="#3066f2"
                                    @click="handleValidateButtonClick">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 150 ? windowWidth / 2.66 : 120) + 'px;'"
                                    :disabled="model.account === null || countdown < 60" @click="handleGetVerifyCode">
                                    <template #icon>
                                        <n-icon>
                                            <ChatboxIcon />
                                        </n-icon>
                                    </template>
                                    {{ countdown < 60 ? `${countdown}秒` : '获取验证码' }} </n-button>
                                        <n-button round
                                            :style="'width:' + (windowWidth / 2.66 < 160 ? windowWidth / 2.66 : 140) + 'px;'"
                                            @click="handleSwitch">
                                            <template #icon>
                                                <n-icon>
                                                    <RepeatIcon />
                                                </n-icon>
                                            </template>
                                            切换登录方式
                                        </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>
        </n-card>
    </n-layout>
</template>
<script setup>
    import logo from '../../assets/fyg/logo.png'
    import { getUrlKey } from '../../utils/SysUtil';
    import { encode, decode } from 'js-base64';
    import { doPost } from "../../utils/AxiosUtil";
    import { onMounted, ref, watch, onUnmounted } from "vue";
    import { useRouter } from "vue-router";
    import VueCookies from 'vue-cookies';
    import { LogInOutline as LogInIcon, Repeat as RepeatIcon, ChatboxOutline as ChatboxIcon } from '@vicons/ionicons5'
    import { NLayout, NCard, NForm, NFormItem, NInput, NRow, NCol, NButton, NButtonGroup, NIcon } from 'naive-ui';
    const { push } = useRouter()
    const historyPath = VueCookies.get('historyPath')
    // console.info(historyPath.replace(/^.+?currentUser\=/,''))
    const currentUser = getUrlKey('currentUser', historyPath)

    const countdown = ref(60);
    const ifSwitch = ref(false);

    function handleSwitch() {
        ifSwitch.value = !ifSwitch.value
        // 切换登录方式时重置倒计时
        countdown.value = 60;
    }

    // 处理获取验证码按钮点击
    const handleGetVerifyCode = () => {
        // 手动验证手机号
        if (!model.value.account) {
            $message.error("请输入手机号");
            return;
        }

        // 验证手机号格式
        if (!/^1[3-9]\d{9}$/.test(model.value.account)) {
            $message.error("请输入正确的手机号");
            return;
        }

        // 开始倒计时
        countdown.value = 59;
        countdownTimer();

        // 这里添加获取验证码的API调用
        let url = '/aliapi/sms/authCode?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
        doPost(url, { mobile: model.value.account }).then(res => {

            if (res.code == '00000') {
                $message.success("验证码已发送");
            } else {
                $message.error(res.msg);
                // 如果API调用失败，重置倒计时
                countdown.value = 60;
            }
        }).catch(err => {
            $message.error(err);
            // 如果API调用出错，重置倒计时
            countdown.value = 60;
        });
    }

    // 优化倒计时函数，使用let声明计时器变量以便清除
    const countdownTimer = () => {
        if (countdown.value > 0) {
            setTimeout(() => {
                countdown.value--;
                countdownTimer();
            }, 1000);
        } else {
            countdown.value = 60;
        }
    }


    const formRef = ref(null);
    const rPasswordFormItemRef = ref(null);
    const modelRef = ref({
        account: null,
        password: null,
    });

    const model = modelRef

    const rules = {
        account: [
            {
                required: true,
                validator(rule, value) {
                    if (!value) {
                        if (ifSwitch.value) {
                            if (!/^1[3-9]\d{11}$/.test(value)) {
                                return new Error("请输入正确的手机号");
                            }
                        }
                        return new Error("请输入账号");
                    }
                    return true;
                },
                trigger: ["input", "blur"]
            }
        ],
        password: [
            {
                required: true,
                validator(rule, value) {
                    if (!value) {
                        if (ifSwitch.value) {
                            if (!/^\d{4}$/.test(value)) {
                                return new Error("请输入4位数字验证码");
                            }
                        }
                        return new Error("请输入密码");
                    }
                    return true;
                },
                trigger: ["input", "blur"]
            }
        ],
    };

    const handlePasswordInput = () => {
        if (modelRef.value.reenteredPassword) {
            rPasswordFormItemRef.value?.validate({ trigger: "password-input" });
        }
    }
    const handleValidateButtonClick = (e) => {
        e.preventDefault();
        formRef.value?.validate((errors) => {
            if (!errors) {
                let url = '/aliapi/auth/jwt?appcode=d6388b546ee444b2888dc2bcb94f1eb9'
                let param = {};
                if (ifSwitch.value) {
                    // 处理验证码登录逻辑
                    // 这里添加验证码登录的API调用
                    url = '/aliapi/auth/jwt/sms?appcode=d6388b546ee444b2888dc2bcb94f1eb9'
                    param = {
                        mobile: model.value.account,
                        code: model.value.password
                    }
                } else {
                    // 处理密码登录逻辑
                    param = {
                        username: model.value.account,
                        password: model.value.password
                    }
                }
                doPost(url, param).then(res => {
                    if (res.code == '00000') {
                        $message.success("验证成功");
                        let jsonData = weappJwtDecode(res.data)
                        // let jsonData = getJsonObj(getJWTStr(decodeStr))
                        // VueCookies.config(jsonData.exp)
                        let date = new Date()
                        let timestamp = date.getTime() / 1000
                        console.info('json', jsonData)
                        const d = jsonData.exp - timestamp
                        const day = d / 60 / 60 / 24
                        VueCookies.set('easId', jsonData.easId, day + 'd')
                        VueCookies.set('account', param.username, day + 'd')
                        VueCookies.set('x-token', res.data, day + 'd')


                        push(historyPath != null ? historyPath : '/')
                    } else {
                        $message.success(res.msg);
                    }
                }).catch(err => {
                    console.info('err', err)
                    $message.error(err);
                })
            } else {
                console.log(errors);
                $message.error("验证失败");
            }
        });
    }
    const getJsonObj = (str) => {
        let jsonData = JSON.parse(str)
        return jsonData
    }

    const getJWTStr = (str) => {
        let firstIndex = str.indexOf('}') + 1
        let secondIndex = str.indexOf('}', firstIndex) + 1

        return str.slice(firstIndex, secondIndex)
    }

    function weappJwtDecode(token, options) {
        if (typeof token !== "string") {
            throw ("Invalid token specified");
        }
        options = options || {};
        var pos = options.header === true ? 0 : 1;
        try {
            return JSON.parse(decode(token.split(".")[pos]));
        }
        catch (e) {
            throw ("Invalid token specified: " + e.message);
        }
    }
    const windowWidth = ref(document.documentElement.clientWidth)
    const windowHeight = ref(document.documentElement.clientHeight)

    watch(windowHeight, (newValue, oldValue) => {
        // console.info("实时屏幕高度：", newValue)
    })
    watch(windowWidth, (newValue, oldValue) => {
        // console.info("实时屏幕宽度：", newValue)
        // dr.value.width = newValue / 2 + newValue / 4;
    })
    onMounted(() => {
        window.onresize = () => {
            return (() => {
                window.fullHeight = document.documentElement.clientHeight
                window.fullWidth = document.documentElement.clientWidth
                windowHeight.value = window.fullHeight
                windowWidth.value = window.fullWidth
            })()
        }
        let str = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        console.info('decode', weappJwtDecode(str))
    })

    // 在组件卸载时清除计时器
    onUnmounted(() => {
        if (countdownTimer) {
            clearTimeout(countdownTimer);
        }
    });

</script>
<style scoped>
    .n-card {
        max-width: 400px;
    }
</style>