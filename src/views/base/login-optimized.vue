<template>
    <n-layout embedded
        :content-style="'height: ' + windowHeight + 'px;padding: 80px;display: flex;justify-content: center;background-color: #1d2748'">
        <n-card :bordered="false" style="border-radius: 15px;"
            :content-style="'margin-top:' + windowHeight / 15 + 'px;'"
            :style="'height:' + (windowHeight / 2.5 + windowHeight / 5) + 'px'">
            <template #cover>
                <div style="background-color:#1d2748;">
                    <img :src="logo">
                </div>
            </template>
            
            <!-- 密码登录表单 -->
            <n-form v-if="!isCodeLogin" ref="formRef" :model="loginForm" :rules="validationRules" 
                label-placement="left" label-width="auto">
                <n-form-item path="account" label="用户名">
                    <n-input v-model:value="loginForm.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="密码">
                    <n-input v-model:value="loginForm.password" type="password" 
                        @input="handlePasswordInput" @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    :disabled="!loginForm.account" color="#3066f2"
                                    :loading="isLoading"
                                    @click="handleLogin">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 200 ? windowWidth / 2.66 : 160) + 'px;'"
                                    @click="toggleLoginMode">
                                    <template #icon>
                                        <n-icon>
                                            <RepeatIcon />
                                        </n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>

            <!-- 验证码登录表单 -->
            <n-form v-else ref="formRef" :model="loginForm" :rules="validationRules" 
                label-placement="left" label-width="auto">
                <n-form-item path="account" label="手机号">
                    <n-input v-model:value="loginForm.account" @keydown.enter.prevent />
                </n-form-item>
                <n-form-item path="password" label="验证码">
                    <n-input v-model:value="loginForm.password" @input="handlePasswordInput" 
                        @keydown.enter.prevent />
                </n-form-item>
                <n-row :gutter="[0, 24]">
                    <n-col :span="24">
                        <div style="display: flex; justify-content: center;">
                            <n-button-group>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 100 ? windowWidth / 2.66 : 80) + 'px;'"
                                    :disabled="!loginForm.account" color="#3066f2"
                                    :loading="isLoading"
                                    @click="handleLogin">
                                    <template #icon>
                                        <n-icon>
                                            <LogInIcon />
                                        </n-icon>
                                    </template>
                                    登录
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 150 ? windowWidth / 2.66 : 120) + 'px;'"
                                    :disabled="!loginForm.account || countdown < COUNTDOWN_DURATION" 
                                    :loading="isSendingCode"
                                    @click="handleGetVerifyCode">
                                    <template #icon>
                                        <n-icon>
                                            <ChatboxIcon />
                                        </n-icon>
                                    </template>
                                    {{ countdown < COUNTDOWN_DURATION ? `${countdown}秒` : '获取验证码' }}
                                </n-button>
                                <n-button round
                                    :style="'width:' + (windowWidth / 2.66 < 160 ? windowWidth / 2.66 : 140) + 'px;'"
                                    @click="toggleLoginMode">
                                    <template #icon>
                                        <n-icon>
                                            <RepeatIcon />
                                        </n-icon>
                                    </template>
                                    切换登录方式
                                </n-button>
                            </n-button-group>
                        </div>
                    </n-col>
                </n-row>
            </n-form>
        </n-card>
    </n-layout>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import VueCookies from 'vue-cookies';
import { decode } from 'js-base64';
import { doPostAsync } from '../../utils/AxiosUtil';
import logo from '../../assets/fyg/logo.png';
import { LogInOutline as LogInIcon, Repeat as RepeatIcon, ChatboxOutline as ChatboxIcon } from '@vicons/ionicons5';
import {
  NLayout,
  NCard,
  NForm,
  NFormItem,
  NInput,
  NRow,
  NCol,
  NButton,
  NButtonGroup,
  NIcon,
  type FormInst,
  type FormValidationError,
  type FormItemRule
} from 'naive-ui';

// 常量定义
const API_ENDPOINTS = {
  SMS_CODE: '/aliapi/sms/authCode?appcode=d6388b546ee444b2888dc2bcb94f1eb9',
  LOGIN_PASSWORD: '/aliapi/auth/jwt?appcode=d6388b546ee444b2888dc2bcb94f1eb9',
  LOGIN_SMS: '/aliapi/auth/jwt/sms?appcode=d6388b546ee444b2888dc2bcb94f1eb9'
} as const;

const VALIDATION_PATTERNS = {
  MOBILE: /^1[3-9]\d{9}$/,
  SMS_CODE: /^\d{4}$/
} as const;

const COUNTDOWN_DURATION = 60;
const SUCCESS_CODE = '00000';

// 类型定义
interface LoginModel {
  account: string | null;
  password: string | null;
}

interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T;
}

interface JWTPayload {
  sub: string;
  userId: string;
  authorities: string[];
  exp: number;
  iat: number;
  jti: string;
  username: string;
  easId?: string;
}

interface LoginParams {
  username?: string;
  password?: string;
  mobile?: string;
  code?: string;
}

// Composables
const router = useRouter();
const message = useMessage();

// 响应式状态
const isCodeLogin = ref<boolean>(false);
const countdown = ref<number>(COUNTDOWN_DURATION);
const isLoading = ref<boolean>(false);
const isSendingCode = ref<boolean>(false);
const windowWidth = ref<number>(document.documentElement.clientWidth);
const windowHeight = ref<number>(document.documentElement.clientHeight);

// 表单相关
const formRef = ref<FormInst | null>(null);
const loginForm = ref<LoginModel>({
  account: null,
  password: null,
});

// 计算属性
const historyPath = computed(() => VueCookies.get('historyPath') as string | null);

// 定时器管理
let countdownTimer: number | null = null;

// 表单验证规则
const validationRules = computed(() => ({
  account: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          return new Error(isCodeLogin.value ? "请输入手机号" : "请输入账号");
        }
        if (isCodeLogin.value && !VALIDATION_PATTERNS.MOBILE.test(value)) {
          return new Error("请输入正确的手机号");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
  password: [
    {
      required: true,
      validator(_rule: FormItemRule, value: string) {
        if (!value) {
          return new Error(isCodeLogin.value ? "请输入验证码" : "请输入密码");
        }
        if (isCodeLogin.value && !VALIDATION_PATTERNS.SMS_CODE.test(value)) {
          return new Error("请输入4位数字验证码");
        }
        return true;
      },
      trigger: ["input", "blur"]
    }
  ],
}));

// 工具函数
const resetCountdown = (): void => {
  countdown.value = COUNTDOWN_DURATION;
  if (countdownTimer !== null) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

const startCountdown = (): void => {
  countdown.value = COUNTDOWN_DURATION - 1;
  countdownTimer = window.setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      resetCountdown();
    }
  }, 1000);
};

const validateMobile = (mobile: string): boolean => {
  if (!mobile) {
    message.error("请输入手机号");
    return false;
  }
  if (!VALIDATION_PATTERNS.MOBILE.test(mobile)) {
    message.error("请输入正确的手机号");
    return false;
  }
  return true;
};

const weappJwtDecode = (token: string, options: { header?: boolean } = {}): JWTPayload => {
  if (typeof token !== "string") {
    throw new Error("Invalid token specified");
  }
  
  const pos = options.header === true ? 0 : 1;
  try {
    return JSON.parse(decode(token.split(".")[pos]));
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : 'Unknown error';
    throw new Error("Invalid token specified: " + errorMessage);
  }
};

const saveTokenAndRedirect = (tokenData: string, loginParams: LoginParams): void => {
  try {
    const jsonData = weappJwtDecode(tokenData);
    const timestamp = Date.now() / 1000;
    const expirationDays = (jsonData.exp - timestamp) / (60 * 60 * 24);
    
    VueCookies.set('easId', jsonData.easId || '', `${expirationDays}d`);
    VueCookies.set('account', loginParams.username || loginParams.mobile || '', `${expirationDays}d`);
    VueCookies.set('x-token', tokenData, `${expirationDays}d`);
    
    router.push(historyPath.value || '/');
  } catch (error) {
    console.error('Token processing error:', error);
    message.error('登录信息处理失败');
  }
};

// 事件处理器
const toggleLoginMode = (): void => {
  isCodeLogin.value = !isCodeLogin.value;
  resetCountdown();
  // 清空表单
  loginForm.value.account = null;
  loginForm.value.password = null;
  // 重置表单验证状态
  nextTick(() => {
    formRef.value?.restoreValidation();
  });
};

const handlePasswordInput = (): void => {
  // 预留接口，当前无需特殊处理
};

const handleGetVerifyCode = async (): Promise<void> => {
  if (!validateMobile(loginForm.value.account || '')) {
    return;
  }
  
  isSendingCode.value = true;
  
  try {
    const response = await doPostAsync<ApiResponse>(API_ENDPOINTS.SMS_CODE, {
      mobile: loginForm.value.account
    });
    
    if (response.code === SUCCESS_CODE) {
      message.success("验证码已发送");
      startCountdown();
    } else {
      message.error(response.msg || '发送验证码失败');
    }
  } catch (error) {
    console.error('Send SMS error:', error);
    message.error('发送验证码失败，请稍后重试');
  } finally {
    isSendingCode.value = false;
  }
};

const handleLogin = async (): Promise<void> => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
  } catch (errors) {
    console.log('Validation errors:', errors);
    message.error("请检查输入信息");
    return;
  }
  
  isLoading.value = true;
  
  const url = isCodeLogin.value ? API_ENDPOINTS.LOGIN_SMS : API_ENDPOINTS.LOGIN_PASSWORD;
  const params: LoginParams = isCodeLogin.value 
    ? {
        mobile: loginForm.value.account || undefined,
        code: loginForm.value.password || undefined
      }
    : {
        username: loginForm.value.account || undefined,
        password: loginForm.value.password || undefined
      };
  
  try {
    const response = await doPostAsync<ApiResponse>(url, params);
    
    if (response.code === SUCCESS_CODE) {
      message.success("登录成功");
      saveTokenAndRedirect(response.data, params);
    } else {
      message.error(response.msg || '登录失败');
    }
  } catch (error) {
    console.error('Login error:', error);
    message.error('登录失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};

// 窗口大小监听
const updateWindowSize = (): void => {
  windowHeight.value = document.documentElement.clientHeight;
  windowWidth.value = document.documentElement.clientWidth;
};

// 生命周期
onMounted(() => {
  window.addEventListener('resize', updateWindowSize);
  
  // 开发环境测试（可选）
  if (process.env.NODE_ENV === 'development') {
    const testToken = '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    console.info('Test JWT decode:', weappJwtDecode(testToken));
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', updateWindowSize);
  if (countdownTimer !== null) {
    clearInterval(countdownTimer);
  }
});

// 监听器
watch(windowHeight, () => {
  // 可以在这里添加窗口高度变化的处理逻辑
});

watch(windowWidth, () => {
  // 可以在这里添加窗口宽度变化的处理逻辑
});
</script>

<style scoped>
.n-card {
  max-width: 400px;
}
</style>
