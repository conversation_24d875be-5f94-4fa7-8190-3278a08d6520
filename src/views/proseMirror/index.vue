<template>
    <div>
        <div ref="editor" class="ProseMirror"></div>
        <button @click="applyDiff">应用变更</button>
        <p class="hint">编辑文本后点击“应用变更”按钮查看删除部分加横线，新增部分显示红色。</p>
    </div>
</template>

<script lang="ts" setup>
    import { ref, onMounted } from 'vue'
    import { EditorState } from 'prosemirror-state'
    import { EditorView } from 'prosemirror-view'
    import { Schema, MarkSpec, Fragment, DOMParser, Mark, DOMSerializer } from 'prosemirror-model'
    import { schema as basicSchema } from 'prosemirror-schema-basic'
    import { history, undo, redo } from 'prosemirror-history'
    import { keymap } from 'prosemirror-keymap'
    import { baseKeymap } from 'prosemirror-commands'
    import { diffChars } from 'diff'

    // 删除标记配置
    const deletionMark: MarkSpec = {
        parseDOM: [{ tag: "s" }],
        toDOM() {
            return ["s", { style: "text-decoration: line-through;" }, 0]
        }
    }

    // 新增标记配置
    const insertionMark: MarkSpec = {
        parseDOM: [{
            tag: "span",
            getAttrs: (dom: any) => (dom.style.color === 'red' ? {} : false)
        }],
        toDOM() {
            return ["span", { style: "color: red;" }, 0]
        }
    }

    // 扩展Schema
    const mySchema = new Schema({
        nodes: basicSchema.spec.nodes,
        marks: basicSchema.spec.marks
            .addToEnd("deletion", deletionMark)
            .addToEnd("insertion", insertionMark)
    })

    // 初始配置
    const initialText = `<div contenteditable="true" translate="no" class="ProseMirror"><p><s style="text-decoration: line-through;">这里</s>输入22<s style="text-decoration: line-through;">22</s>原1111文，可以随<span style="color: red;">1111</span>意编辑</p></div>`;
    const baseText = ref('')
    const editor = ref<HTMLDivElement | null>(null)
    let view: EditorView | null = null

    function setEditorContentFromHTML(htmlString: string) {
        if (!view) return;

        // 创建一个临时的 DOM 容器解析 HTML
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = htmlString;

        // 使用 ProseMirror 的 DOMParser 解析 HTML
        const newDoc = DOMParser.fromSchema(mySchema).parse(tempDiv);

        // 创建新的编辑器状态
        const newState = EditorState.create({
            doc: newDoc,
            plugins: view.state.plugins
        });

        // 更新 EditorView
        view.updateState(newState);
    }


    function initEditor() {

        if (!editor.value) return;

        // 先创建一个空的文档
        const emptyDoc = mySchema.nodes.doc.create(null, [mySchema.nodes.paragraph.create(null)]);

        view = new EditorView(editor.value, {
            state: EditorState.create({
                doc: emptyDoc, // 先设置一个空文档
                plugins: [
                    history(),
                    keymap(baseKeymap),
                    keymap({
                        "Mod-z": () => undo(view!.state, view!.dispatch),
                        "Mod-y": (state, dispatch) => redo(state, dispatch)
                    })
                ]
            })
        });

        // 解析并填充 HTML
        setEditorContentFromHTML(initialText);

        // 确保 baseText.value 获取的是最新的文本
        setTimeout(() => {
            baseText.value = view!.state.doc.textContent;
        }, 0);
    }

    onMounted(() => {
        initEditor();
    })

    type TextWithMarks = {
        text: string;
        marks?: Mark[];
    };

    function getState(doc: any): TextWithMarks[] {
        const state: TextWithMarks[] = [];
        doc.descendants((node: any) => {
            if (node.isText) {
                state.push({
                    text: node.text,
                    marks: Array.from(node.marks).map((mark: any) => mark.type.name)
                });
            }
            return true;
        });
        return state;
    }

    interface DiffPart {
        added?: boolean;
        removed?: boolean;
        value: string;
    }


    // 辅助函数：提取不包含删除标记的纯文本
    function getRawText(doc: any): string {
        let text = "";
        doc.descendants((node: any) => {
            if (node.isText) {
                if (node.marks.some((mark: any) => mark.type.name === "insertion")) {
                    // 使用不太可能出现在正常文本中的标记符
                    text += `<<INS>>${node.text}<<END>>`;
                } else if (!node.marks.some((mark: any) => mark.type.name === "deletion")) {
                    text += node.text;
                }
            }
            return true;
        });
        return text;
    }

    function getEditorHTML(): string {
        if (!view) return "";
        // 根据当前的 Schema 创建一个序列化器
        const serializer = DOMSerializer.fromSchema(mySchema);
        // 将当前文档内容转换为 DOM Fragment
        const fragment = serializer.serializeFragment(view.state.doc.content);
        // 创建一个临时容器元素
        const tempDiv = document.createElement("div");
        tempDiv.appendChild(fragment);
        // 获取容器内的 HTML 字符串
        return tempDiv.innerHTML;
    }
    // 使用专业diff算法处理变更
    function applyDiff() {
        if (!view) return

        const currentState = view.state

        // 使用 getRawText 获取剔除删除标记后的文本
        const currentRawText = getRawText(currentState.doc)


        const diffs = diffChars(baseText.value, currentRawText)

        // 获取当前文档结构
        const originalDoc = currentState.doc

        let mark = 0;
        // 构建带标记的文本节点数组
        const textNodes = diffs.reduce((nodes, part, index) => {
            let marks: any[] = [];
            let value = part.value;

            // 如果检测到特殊的 insertion 标记
            // if (value.indexOf("<<INS>>") !== -1 && value.indexOf("<<END>>") !== -1) {
            //     // 移除标记符
            //     value = value.replace(/<<INS>>/g, "").replace(/<<END>>/g, "");
            //     marks.push(mySchema.marks.insertion.create());
            // } else {
            //     if (part.added) marks.push(mySchema.marks.insertion.create());
            //     if (part.removed) marks.push(mySchema.marks.deletion.create());
            // }

            if (mark === 0) {
                if (value.indexOf("<<INS>>") !== -1) {
                    mark = 1;
                    value = value.replace(/<<INS>>/g, "")
                } else {
                    if (part.added) marks.push(mySchema.marks.insertion.create());
                    if (part.removed) marks.push(mySchema.marks.deletion.create());
                }
            } else if (mark === 1) {
                mark = 2;
                marks.push(mySchema.marks.insertion.create());

            } else {
                if (value.indexOf("<<END>>") !== -1) {
                    mark = 0;
                    value = value.replace(/<<END>>/g, "");
                }
            }
            if (value) {
                nodes.push(mySchema.text(value, marks));
            }
            return nodes;
            // const marks = []

            // if (part.added) marks.push(mySchema.marks.insertion.create())
            // if (part.removed) marks.push(mySchema.marks.deletion.create())

            // if (part.value) {
            //     nodes.push(mySchema.text(part.value, marks))
            // }
            // return nodes
        }, [] as any[])

        // 创建新段落时保留原节点类型
        const newParagraph = originalDoc.content.firstChild!.copy(Fragment.from(textNodes))
        const newDoc = mySchema.nodes.doc.create(null, [newParagraph])

        // 计算正确的替换范围
        const docSize = currentState.doc.content.size
        const tr = currentState.tr
            .replaceRange(0, docSize, new Slice(Fragment.from(newDoc), 0, 0))
            .setMeta("addToHistory", false) // 不记录到撤销栈
            .setStoredMarks([]) // 清除光标处的存储标记

        // 应用事务并更新基准文本
        view.dispatch(tr)
        const currentText = view.state.doc.textContent
        baseText.value = currentText // 更新基准文本为当前文本，这样后续编辑不会被标记
        console.log(`output->editor`, getEditorHTML())
    }

    // 辅助函数：ProseMirror Slice引用
    import { Slice } from 'prosemirror-model'
</script>

<style>
    .ProseMirror {
        border: 1px solid #ddd;
        padding: 10px;
        min-height: 200px;
        font-size: 16px;
        line-height: 1.5;
        white-space: pre-wrap;
    }

    .ProseMirror s {
        text-decoration: line-through !important;
        color: #666;
        background: #fff0f0;
    }

    .ProseMirror span[style*="red"] {
        color: #ff4444 !important;
        background: #f8fff0;
    }

    .hint {
        margin-top: 10px;
        color: #666;
        font-size: 0.9em;
    }

    button {
        margin-top: 10px;
        padding: 8px 16px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s;
    }

    button:hover {
        background: #337ecc;
    }

    button:active {
        background: #2a6cad;
    }
</style>