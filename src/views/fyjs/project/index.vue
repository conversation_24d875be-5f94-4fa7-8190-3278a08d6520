<template>
    <n-space style="display: flex; justify-content: space-between;margin: 4px 4px 4px 4px;">
        <n-button-group type="primary">
            <n-popover style="padding: 0 0 0 0;" position="br" trigger="click">
                <template #trigger>
                    <n-button class="button">字段配置</n-button>
                </template>

                <template #default>
                    <column-drag :app-remark="'方远建设项目管理用户配置'" :id="settingId" :data="userSetting" :user-id="userId"
                        :app-key="appKey" :app-router="appRouter" @update:column="handleColumnUpdate" />
                </template>
            </n-popover>
        </n-button-group>
    </n-space>
    <n-data-table ref="ftable" remote :columns="pageData.columns.column" :data="pageData.data.content" :loading="loading"
        :pagination="paginationReactive" :bordered="false" @update-filters="combinedHandler" />
</template>
<script setup lang="ts">
import { formatter, parser, } from '../../../fconfig/base';
import { fetchData, getPermission, deserializeColumns, getColorObj } from '../../../utils/APIUtils';
import { createColumnWithFilterOptions, createColumnWithTemplate, createColumnWithDatePickerTemplate, optionsMap, DEFAULT_SETTING } from '../../../fconfig/fyjs/project/management/index';
import { doPostAsync } from '../../../utils/AxiosUtil';
import type { FyjsProjectManagement as ProjectData, MainProject, QueryParam, FyjsProjectManagementTable as TableData, ExtendedDataTableBaseColumn, SETTING } from '../../../fconfig/fyjs/project/management/index'
import { h, } from 'vue'
import { NTag, NButton } from 'naive-ui'
import {
    DataTableColumns,
    DataTableBaseColumn,
    DataTableFilterState,
} from 'naive-ui'
import myTag from '../../../components/MyTagComponent.vue'
import columnDrag from '../../../components/dragger/draggableSetting.vue'
import { inject } from 'vue';

const permissionId = ref<number | undefined>(99);
const formatNumber = (num: any): string => {
    if (num === undefined || num === null) {
        return '';
    }
    try {
        if (num.includes('*')) {
            return num;
        } else {
            return new Intl.NumberFormat('en-US').format(num);
        }
    } catch (err) {
        return '';
    }
};
const BASE_SERVER = '/fyjs/projects/';

//setting
const userId = 'xlh';//正式使用时需要获取用户账号
const appKey = 'fyjs_project_management_2' //自定义当前项目的key
const appRouter = 'index' //当前页面的route
const settingId = undefined 

// 更新获取 userSetting 的函数
const getUserSetting = async () => {
    const url = '/fyschedule/app/user/setting/find'
    const params = {
        userId: userId,
        appKey: appKey,
        appRouter: appRouter
    }
    try {
        const data: any = await doPostAsync(url, params);
        if (data.code === 0) {
            if (data.result?.appSetting) {
                // 反序列化
                const deserializedColumns = deserializeColumns(data.result.appSetting);
                // 更新 userSetting
                Object.assign(userSetting, deserializedColumns);
                const { columnsArray, columnsMap } = setColumn(userSetting);
                Object.assign(columns, { column: columnsArray });
                console.log(`Updated columns: `, columnsArray);
            }
        }
    } catch (error) {
        console.error('Failed to fetch user setting:', error);
    }
}

const getColumn = (items: any) => {
    return items.filter((item: any) => item.isActivated !== false);
}

type FilterData = {
    key: string,
    value: any
}
const doFilter = async (val: FilterData) => {
    if (Array.isArray(val.value)) {
        let raw = normalizeValues(val.value);
        queryParam[val.key] = JSON.stringify(raw);
    } else {
        queryParam[val.key] = val.value;
    }
    await handleSearch()
    console.log(`output->val`, val)
}

type Field = {
    key: string,
    data: ExtendedDataTableBaseColumn,
    type: string,
    toDo?: Function,
    optionKey?: keyof typeof optionsMap,
}

const fields: Field[] = [{
    key: 'projectName',
    data: { title: '项目名称', key: 'projectName', width: 400, isActivated: true },
    type: 'text',
    toDo: doFilter
}, {
    key: 'contractType',
    data: {
        title: '承包类型', key: 'contractType', width: 120, render: (_: any, index: number) => {
            return _.contractType ? h(myTag, {
                value: _.contractType,
                colorObject: getColorObj(_.contractType),
            }) : undefined
        }, isActivated: true
    },
    type: 'options',
    optionKey: 'contractType'
}, {
    key: 'singleOrGroup',
    data: {
        title: '单体/群体', key: 'singleOrGroup', width: 140, render: (_: any, index: number) => {
            return typeof _.singleOrGroup !== 'undefined' && _.singleOrGroup !== null ? h(NTag, {
                bordered: false,
                type: _.singleOrGroup === 0 ? 'warning' : 'success'
            }, {
                default: () => _.singleOrGroup === 0 ? '单体' : _.singleOrGroup === 1 ? '群体' : undefined
            }) : undefined;
        }, isActivated: true
    },
    type: 'options',
    optionKey: 'singleOrGroup'
}, {
    key: 'contractSigningDate',
    data: { title: '合同签订日期', key: 'contractSigningDate', width: 140, isActivated: true },
    type: 'datePicker',
    toDo: doFilter
},]

const actions = {
        title: '操作',
        key: 'actions',
        width: 180,
        render(row:any) {
            return h(
                NButton,
                {
                    strong: true,
                    size: 'small',
                    type: 'primary',
                    onClick() {
                        message.info('编辑');
                    },
                }, {
                    default: () => '编辑'
                }
            )
        },
        isActivated: true
    }

// 定义 setColumn 函数，根据 userSetting 动态生成 columns
const setColumn = (settings: SETTING[]) => {
    let columnsArray: ExtendedDataTableBaseColumn[] = [];
    let columnsMap: any = {};

    settings.forEach((setting) => {
        if (setting.isActivated) {
            let column: any;
            const field = fields.find(f => f.key === setting.key);
            if (field) {
                if (field.type === 'text') {
                    column = createColumnWithTemplate(field.data, field.type, field.toDo);
                } else if (field.type === 'options') {
                    if (field.optionKey)
                        column = createColumnWithFilterOptions(field.data, field.optionKey);
                } else if (field.type === 'datePicker') {
                    column = createColumnWithDatePickerTemplate(field.data, field.type, field.toDo);
                }
                // 添加到数组
                columnsArray.push(column);
                // 同时添加到映射
                columnsMap[field.data.key] = column;
            }
        }
    });
    columnsArray.push(actions);
    return { columnsArray, columnsMap };
}

// 获取列数据
const userSetting = reactive<SETTING[]>(DEFAULT_SETTING);
const { columnsArray, columnsMap } = setColumn(userSetting);

const handleColumnUpdate = (e: any) => {
    if (e) {
        const { columnsArray, columnsMap } = setColumn(userSetting);
        Object.assign(columns, { column: columnsArray });
    }
}

//table 
const ftable = ref<any>(null);
const loadingRef = ref(true)
const loading = loadingRef
const columns = reactive<any>({
    column: [] 
})

//filter
const handleFilters = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
    const key = initiatorColumn?.key;
    if (key && filters[key]) {
        // Ensure the values are treated as an array of strings
        const filterValues = filters[key];
        let filteredValues: string[] = [];

        if (Array.isArray(filterValues)) {
            // Assuming FilterOptionValue can be directly used as string
            filteredValues = filterValues.map(value => String(value));
        } else if (filterValues != null) {
            // Handle a single FilterOptionValue or non-array
            filteredValues = [String(filterValues)];
        }

        // Now filteredValues is guaranteed to be string[], can use it safely
        if (filteredValues.length > 0) {
            let raw = normalizeValues(filteredValues);
            queryParam[key + 'JsonStr'] = JSON.stringify(raw);
            handleSearch();
        } else {
            delete queryParam[key + 'JsonStr'];
            handleSearch();
        }
        columnsMap[key].filterOptionValues = filters[key]
    }
}

function normalizeValues(values: string[]): (number | string | null)[] {
    return values.map(value => {
        // 检查是否为数字类型的字符串，如果是，则转换为数字
        if (/^\d+$/.test(value)) {
            return Number(value);
        } else {
            //不是数字的直接返回就好了
            return value;
        }
    }).filter(item => item !== null);
}


const anotherHandler = (filters: any) => {
    console.log('Second handler:', filters);
    // 其他处理逻辑
};

// 合并这两个处理器到一个事件监听器
const combinedHandler = (filters: DataTableFilterState, initiatorColumn?: DataTableBaseColumn) => {
    handleFilters(filters, initiatorColumn);
    anotherHandler(filters);
};

const data = reactive<TableData>({});

const initialPaginationState = {
    itemCount: 0,
    pageCount: 1,
    page: 1,
    pageSize: 15,
    showSizePicker: true,
    pageSizes: [15, 25, 50],
    onChange: (page: number) => {
        paginationReactive.page = page;
        handleSearch();
    },
    onUpdatePageSize: (pageSize: number) => {
        paginationReactive.pageSize = pageSize
        paginationReactive.page = 1
        handleSearch();
    },
    prefix({ itemCount }: { itemCount?: number }) {
        return `Total is ${itemCount}.`
    }
}

const paginationReactive = reactive({ ...initialPaginationState });

// 定义重置方法
async function resetPagination() {
    Object.assign(paginationReactive, { ...initialPaginationState });
}

const pageData = {
    data: data,
    columns: columns,
    pagination: paginationReactive
}

//query
const initialQueryParam: QueryParam = {
    dataStatus: 0,
    currentPage: paginationReactive.page - 1,
    pageSize: paginationReactive.pageSize,
    projectName: undefined,
    columnKey: 'singleOrGroup',
    order: 'descend'
};

const queryParam = reactive<QueryParam>({ ...initialQueryParam });

async function resetQueryParam() {
    Object.assign(queryParam, { ...initialQueryParam });
}

//data
async function getData(queryParam?: QueryParam) {
    const url = BASE_SERVER + 'filter'
    const results = await fetchData(url, queryParam);
    if (results.length > 0) {
        const result = results[0];
        result.content.forEach((item: ProjectData) => {
            Object.keys(item).forEach(key => {
                // 仅当项的值不是 null 或 undefined 时才继续处理
                if (item[key] != null && typeof item[key] === 'string') {
                    const trimmedValue = item[key].trim();
                    // 如果字符串不为空，尝试转换为数字
                    if (trimmedValue !== '') {
                        const convertedNumber = Number(trimmedValue);
                        // 只有当转换结果为有效数字时，才更新该值
                        if (!isNaN(convertedNumber)) {
                            item[key] = convertedNumber;
                        }
                    }
                }
            });
        });

        Object.assign(data, {
            ...result  // 确保从服务器返回的结果是完整的，包括所有需要的字段
        });
    }
    paginationReactive.pageCount = data.totalPages ? data.totalPages : 0;
    paginationReactive.itemCount = data.totalElements ? data.totalElements : 0;
    setTimeout(() => {
        loadingRef.value = false
    }, 1000);
}

const handleSearch = async () => {
    loadingRef.value = true
    queryParam.pageSize = paginationReactive.pageSize;
    queryParam.currentPage = paginationReactive.page - 1;
    queryParam.permissionId = permissionId.value;
    await getData(queryParam);
}

onMounted(() => {
    getUserSetting();
    handleSearch();
})

// 从全局注入中获取 message 实例
const message: any = inject('message');

</script>
<style scoped></style>