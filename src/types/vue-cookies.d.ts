declare module 'vue-cookies' {
    interface VueCookies {
        get(key: string): string | null;
        set(key: string, value: string, expireTimes?: string | Date | number, path?: string, domain?: string, secure?: boolean): void;
        remove(key: string, path?: string, domain?: string): void;
        isKey(key: string): boolean;
        keys(): string[];
    }

    const VueCookies: VueCookies;

    export default VueCookies;
}
